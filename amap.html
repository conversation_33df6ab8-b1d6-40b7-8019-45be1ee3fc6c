<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具 - 支持道路吸附</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=YOUR_API_KEY&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.CircleEditor,AMap.RectangleEditor,AMap.MarkerClusterer,AMap.Driving"></script>
    
    <link rel="stylesheet" href="amap-style.css">
</head>
<body>
    <div class="app-container">
        <header class="header">
            <h1>🗺️ 高德地图绘制工具</h1>
            <div class="header-controls">
                <button id="clear-all" class="btn btn-danger">清空所有</button>
                <button id="export-data" class="btn btn-primary">导出数据</button>
                <input type="file" id="import-data" accept=".json" style="display: none;">
                <button id="import-btn" class="btn btn-secondary">导入数据</button>
                <button id="toggle-snap" class="btn btn-success active" data-snap="true">道路吸附: 开</button>
            </div>
        </header>

        <div class="main-content">
            <div class="sidebar">
                <div class="tool-section">
                    <h3>绘制工具</h3>
                    <div class="tool-buttons">
                        <button id="draw-marker" class="tool-btn active" data-tool="marker">
                            📍 标记点
                        </button>
                        <button id="draw-polyline" class="tool-btn" data-tool="polyline">
                            📏 绘制线
                        </button>
                        <button id="draw-polygon" class="tool-btn" data-tool="polygon">
                            🔷 绘制面
                        </button>
                        <button id="draw-rectangle" class="tool-btn" data-tool="rectangle">
                            ⬜ 矩形
                        </button>
                        <button id="draw-circle" class="tool-btn" data-tool="circle">
                            ⭕ 圆形
                        </button>
                        <button id="edit-mode" class="tool-btn" data-tool="edit">
                            ✏️ 编辑模式
                        </button>
                        <button id="select-mode" class="tool-btn" data-tool="select">
                            👆 选择模式
                        </button>
                    </div>
                </div>

                <div class="snap-section">
                    <h3>道路吸附设置</h3>
                    <div class="snap-controls">
                        <div class="snap-group">
                            <label for="snap-distance">吸附距离:</label>
                            <input type="range" id="snap-distance" min="10" max="100" value="30">
                            <span id="distance-value">30m</span>
                        </div>
                        <div class="snap-group">
                            <label>
                                <input type="checkbox" id="snap-to-roads" checked>
                                吸附到道路
                            </label>
                        </div>
                        <div class="snap-group">
                            <label>
                                <input type="checkbox" id="snap-to-intersections" checked>
                                吸附到路口
                            </label>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h3>图层信息</h3>
                    <div id="layer-info">
                        <div class="info-item">
                            <span class="info-label">标记点:</span>
                            <span id="marker-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">线条:</span>
                            <span id="line-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">多边形:</span>
                            <span id="polygon-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">选中对象:</span>
                            <span id="selected-info">无</span>
                        </div>
                    </div>
                </div>

                <div class="style-section">
                    <h3>样式设置</h3>
                    <div class="style-controls">
                        <div class="style-group">
                            <label for="stroke-color">线条颜色:</label>
                            <input type="color" id="stroke-color" value="#ff0000">
                        </div>
                        <div class="style-group">
                            <label for="fill-color">填充颜色:</label>
                            <input type="color" id="fill-color" value="#ff0000">
                        </div>
                        <div class="style-group">
                            <label for="stroke-width">线条粗细:</label>
                            <input type="range" id="stroke-width" min="1" max="10" value="3">
                            <span id="width-value">3</span>
                        </div>
                        <div class="style-group">
                            <label for="fill-opacity">填充透明度:</label>
                            <input type="range" id="fill-opacity" min="0" max="1" step="0.1" value="0.3">
                            <span id="opacity-value">0.3</span>
                        </div>
                    </div>
                </div>

                <div class="edit-section">
                    <h3>编辑操作</h3>
                    <div class="edit-controls">
                        <button id="delete-selected" class="btn btn-danger" disabled>删除选中</button>
                        <button id="copy-selected" class="btn btn-secondary" disabled>复制</button>
                        <button id="paste-object" class="btn btn-secondary" disabled>粘贴</button>
                        <button id="undo-action" class="btn btn-secondary">撤销</button>
                        <button id="redo-action" class="btn btn-secondary">重做</button>
                    </div>
                </div>
            </div>

            <div class="map-container">
                <div id="map"></div>
                <div class="map-controls">
                    <div class="coordinates">
                        <span>坐标: </span>
                        <span id="mouse-coordinates">--, --</span>
                    </div>
                    <div class="zoom-level">
                        <span>缩放: </span>
                        <span id="zoom-level">--</span>
                    </div>
                    <div class="current-tool">
                        <span>当前工具: </span>
                        <span id="current-tool-name">标记点</span>
                    </div>
                </div>
                
                <div id="snap-indicator" class="snap-indicator hidden">
                    <div class="snap-point"></div>
                    <div class="snap-text">道路吸附</div>
                </div>
            </div>
        </div>
    </div>

    <script src="amap-script.js"></script>
</body>
</html>
