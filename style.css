* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
    max-width: 500px;
    width: 100%;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.game-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
}

.game-board-container {
    position: relative;
    margin-bottom: 20px;
}

#gameCanvas {
    display: block;
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 5px;
    margin: 0 auto;
}

.game-over {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    border-radius: 5px;
}

.hidden {
    display: none;
}

#restart-btn {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

#restart-btn:hover {
    background-color: #388e3c;
}

.controls {
    text-align: center;
}

h3 {
    margin-bottom: 10px;
    color: #333;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
}

.middle-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 5px 0;
}

.control-btn {
    width: 50px;
    height: 50px;
    margin: 5px;
    font-size: 20px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.control-btn:hover {
    background-color: #388e3c;
}

.instructions {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}
