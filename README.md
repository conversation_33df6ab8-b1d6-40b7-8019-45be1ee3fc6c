# 高德地图绘制工具

一个功能完整的基于高德地图API的交互式地图绘制工具，支持道路自动吸附和完整的编辑功能。

## 功能特点

### 🎨 绘制工具
- **标记点**：在地图上添加标记点
- **绘制线**：支持道路自动吸附的线条绘制
- **绘制面**：多边形区域绘制
- **矩形**：快速绘制矩形区域
- **圆形**：快速绘制圆形区域

### 🧲 道路吸附
- **自动路径规划**：线条绘制时自动贴合道路网络
- **可调节吸附距离**：10-200米范围内调节
- **智能路径优化**：使用高德驾车路径规划API
- **开关控制**：可随时开启/关闭道路吸附功能

### ✏️ 编辑功能
- **选择模式**：点击选择已绘制的对象
- **节点编辑**：拖动节点调整形状
- **移动对象**：整体移动绘制的要素
- **样式修改**：实时调整颜色、线宽、透明度
- **复制粘贴**：快速复制已有对象

### 🗑️ 删除功能
- **单个删除**：删除选中的对象
- **批量清空**：一键清空所有绘制内容
- **删除确认**：防止误操作的确认机制

### 📊 信息显示
- **实时统计**：显示各类要素数量
- **详细信息**：选中对象的坐标、长度、面积等
- **鼠标坐标**：实时显示鼠标位置坐标
- **缩放级别**：当前地图缩放级别

### 💾 数据管理
- **导出功能**：将绘制数据导出为JSON格式
- **导入功能**：加载已有的绘制数据
- **撤销重做**：支持操作历史管理
- **操作记录**：完整的操作历史追踪

## 快速开始

### 1. 获取高德地图API Key

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并登录
3. 创建应用，选择"Web端(JS API)"
4. 获取API Key

### 2. 配置API Key

打开 `amap-draw.html` 文件，找到以下行：

```html
<script src="https://webapi.amap.com/maps?v=2.0&key=YOUR_API_KEY&plugin=..."></script>
```

将 `YOUR_API_KEY` 替换为你的实际API Key。

### 3. 运行应用

直接在浏览器中打开 `amap-draw.html` 文件即可使用。

## 使用说明

### 基本操作

1. **选择工具**：点击左侧工具栏选择绘制工具
2. **开始绘制**：
   - **标记点**：直接点击地图
   - **线条**：点击开始，继续点击添加点，双击结束
   - **多边形**：点击开始，继续点击添加点，双击结束
   - **矩形/圆形**：点击并拖动

### 道路吸附功能

1. **开启吸附**：确保顶部"道路吸附"按钮为开启状态
2. **调节距离**：在左侧面板调整吸附距离
3. **绘制路径**：选择"绘制线"工具，点击地图开始绘制
4. **自动规划**：系统会自动调用路径规划，将直线替换为道路路径

### 编辑操作

1. **选择对象**：点击"选择"工具，然后点击要编辑的对象
2. **查看信息**：选中后在左侧面板查看详细信息
3. **编辑节点**：点击"编辑节点"按钮，拖动节点调整形状
4. **修改样式**：在样式设置区域调整颜色、线宽等
5. **删除对象**：点击"删除选中"按钮

### 数据管理

1. **导出数据**：点击顶部"导出"按钮，下载JSON文件
2. **导入数据**：点击"导入"按钮，选择之前导出的JSON文件
3. **撤销操作**：点击撤销按钮或使用Ctrl+Z
4. **重做操作**：点击重做按钮或使用Ctrl+Y

## 技术架构

### 前端技术
- **HTML5**：页面结构
- **CSS3**：样式和响应式设计
- **JavaScript ES6+**：核心逻辑实现

### 地图服务
- **高德地图JavaScript API 2.0**：地图显示和基础功能
- **高德路径规划API**：道路吸附功能
- **高德几何计算工具**：长度和面积计算

### 核心功能模块
- **绘制工具管理**：AMap.MouseTool
- **编辑器系统**：AMap.PolyEditor, AMap.CircleEditor等
- **路径规划服务**：AMap.Driving
- **几何计算**：AMap.GeometryUtil

## 文件结构

```
├── amap-draw.html          # 主页面文件
├── amap-draw-style.css     # 样式文件
├── amap-draw-script.js     # 核心JavaScript逻辑
└── README.md              # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **API Key配置**：使用前必须配置有效的高德地图API Key
2. **网络连接**：需要稳定的网络连接以加载地图瓦片和调用API
3. **HTTPS要求**：在生产环境中建议使用HTTPS协议
4. **配额限制**：注意高德API的调用配额限制

## 常见问题

### Q: 地图无法显示？
A: 请检查API Key是否正确配置，网络连接是否正常。

### Q: 道路吸附不工作？
A: 确保道路吸附功能已开启，且在有道路的区域进行绘制。

### Q: 无法编辑对象？
A: 确保已选中对象，且对象类型支持编辑（标记点不支持节点编辑）。

### Q: 导入数据失败？
A: 请确保导入的是本工具导出的JSON格式文件。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本绘制功能
- 实现道路自动吸附
- 完整的编辑和删除功能
- 数据导入导出功能
