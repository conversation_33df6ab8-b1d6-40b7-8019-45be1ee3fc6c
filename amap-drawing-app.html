<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具 - 道路吸附版</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.Driving,AMap.Walking"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f0f2f5;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 8px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .sidebar-header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .sidebar-header p {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .tool-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .tool-section h3 {
            font-size: 14px;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .tool-section h3::before {
            content: '';
            width: 3px;
            height: 14px;
            background: #667eea;
            margin-right: 8px;
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .tool-btn {
            padding: 12px 8px;
            border: 2px solid #e1e5e9;
            background: white;
            cursor: pointer;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .tool-btn .icon {
            font-size: 18px;
        }
        
        .tool-btn:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .tool-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .snap-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .snap-toggle label {
            font-size: 13px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .switch.active {
            background: #667eea;
        }
        
        .switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        
        .switch.active::after {
            transform: translateX(20px);
        }
        
        .action-btn {
            width: 100%;
            padding: 12px;
            margin: 5px 0;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }
        
        .action-btn.danger {
            background: #ff4757;
            color: white;
        }
        
        .action-btn.danger:hover {
            background: #ff3742;
        }
        
        .action-btn.primary {
            background: #667eea;
            color: white;
        }
        
        .action-btn.primary:hover {
            background: #5a67d8;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .status-bar {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            font-size: 13px;
            min-width: 200px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
        }
        
        .status-item .label {
            color: #666;
        }
        
        .status-item .value {
            font-weight: 500;
            color: #333;
        }
        
        .tip-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 14px;
            display: none;
            z-index: 1000;
        }
        
        .shortcuts {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 11px;
        }
        
        .shortcuts kbd {
            background: #555;
            border: 1px solid #777;
            border-radius: 3px;
            padding: 2px 5px;
            font-size: 10px;
            margin: 0 2px;
        }
        
        .object-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .object-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
        }
        
        .object-item:hover {
            background: #e9ecef;
        }
        
        .object-item .type {
            color: #667eea;
            font-weight: 500;
        }
        
        .object-actions {
            display: flex;
            gap: 5px;
        }
        
        .object-actions button {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .edit-btn {
            background: #28a745;
            color: white;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🗺️ 地图绘制工具</h1>
                <p>支持道路吸附的专业绘图工具</p>
            </div>
            
            <div class="tool-section">
                <h3>绘制工具</h3>
                <div class="tool-grid">
                    <button class="tool-btn active" data-tool="marker">
                        <span class="icon">📍</span>
                        <span>标记点</span>
                    </button>
                    <button class="tool-btn" data-tool="polyline">
                        <span class="icon">📏</span>
                        <span>绘制线</span>
                    </button>
                    <button class="tool-btn" data-tool="polygon">
                        <span class="icon">🔷</span>
                        <span>绘制面</span>
                    </button>
                    <button class="tool-btn" data-tool="rectangle">
                        <span class="icon">⬜</span>
                        <span>矩形</span>
                    </button>
                    <button class="tool-btn" data-tool="circle">
                        <span class="icon">⭕</span>
                        <span>圆形</span>
                    </button>
                    <button class="tool-btn" data-tool="select">
                        <span class="icon">👆</span>
                        <span>选择</span>
                    </button>
                </div>
            </div>
            
            <div class="tool-section">
                <h3>道路吸附</h3>
                <div class="snap-toggle">
                    <label>
                        🛣️ 启用道路吸附
                    </label>
                    <div class="switch" id="snap-toggle"></div>
                </div>
                <div style="font-size: 11px; color: #666; line-height: 1.4;">
                    开启后，绘制的线条将自动吸附到最近的道路
                </div>
            </div>
            
            <div class="tool-section">
                <h3>操作</h3>
                <button class="action-btn primary" id="save-data">💾 保存数据</button>
                <button class="action-btn primary" id="load-data">📂 加载数据</button>
                <button class="action-btn danger" id="clear-all">🗑️ 清空所有</button>
            </div>
            
            <div class="object-list" id="object-list">
                <h3>绘制对象</h3>
                <div id="objects-container">
                    <div style="text-align: center; color: #999; font-size: 12px; padding: 20px;">
                        暂无绘制对象
                    </div>
                </div>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            
            <div class="status-bar">
                <div class="status-item">
                    <span class="label">当前工具:</span>
                    <span class="value" id="current-tool">标记点</span>
                </div>
                <div class="status-item">
                    <span class="label">道路吸附:</span>
                    <span class="value" id="snap-status">关闭</span>
                </div>
                <div class="status-item">
                    <span class="label">对象数量:</span>
                    <span class="value" id="object-count">0</span>
                </div>
                <div class="status-item">
                    <span class="label">状态:</span>
                    <span class="value" id="status">就绪</span>
                </div>
            </div>
            
            <div id="tip" class="tip-overlay">点击地图开始绘制</div>
            
            <div class="shortcuts">
                <kbd>ESC</kbd> 取消绘制 | <kbd>Del</kbd> 删除选中 | <kbd>Ctrl+Z</kbd> 撤销
            </div>
        </div>
    </div>
    
    <script src="amap-drawing-app.js"></script>
</body>
</html>
