<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础功能测试</title>
    
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        
        button {
            padding: 10px 15px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        #output {
            margin-top: 20px;
            padding: 10px;
            background-color: #f1f1f1;
            border-radius: 4px;
            min-height: 100px;
        }
        
        #map {
            width: 100%;
            height: 400px;
            margin-top: 20px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>基础功能测试</h1>
    
    <div>
        <button onclick="testBasic()">测试基础JavaScript</button>
        <button onclick="testAMapAPI()">测试高德地图API</button>
        <button onclick="testMapCreation()">测试地图创建</button>
        <button onclick="testMouseTool()">测试鼠标工具</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div id="output">点击按钮开始测试...</div>
    
    <div id="map"></div>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool"></script>
    
    <script>
        var map, mouseTool;
        
        function log(message) {
            var output = document.getElementById('output');
            output.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }
        
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }
        
        function testBasic() {
            log('开始测试基础JavaScript功能...');
            
            try {
                // 测试基本功能
                var test = 'Hello World';
                log('✓ 字符串测试通过: ' + test);
                
                var arr = [1, 2, 3];
                log('✓ 数组测试通过: ' + arr.length + ' 个元素');
                
                var obj = { name: 'test', value: 123 };
                log('✓ 对象测试通过: ' + obj.name);
                
                // 测试DOM操作
                var element = document.getElementById('output');
                if (element) {
                    log('✓ DOM操作测试通过');
                } else {
                    log('✗ DOM操作测试失败');
                }
                
                log('基础JavaScript功能测试完成');
            } catch (error) {
                log('✗ 基础JavaScript测试失败: ' + error.message);
            }
        }
        
        function testAMapAPI() {
            log('开始测试高德地图API...');
            
            try {
                if (typeof AMap === 'undefined') {
                    log('✗ 高德地图API未加载');
                    return;
                }
                
                log('✓ 高德地图API已加载');
                log('✓ AMap版本: ' + (AMap.version || '未知'));
                
                // 测试AMap对象的基本属性
                if (AMap.Map) {
                    log('✓ AMap.Map 类可用');
                } else {
                    log('✗ AMap.Map 类不可用');
                }
                
                if (AMap.MouseTool) {
                    log('✓ AMap.MouseTool 插件可用');
                } else {
                    log('✗ AMap.MouseTool 插件不可用');
                }
                
                log('高德地图API测试完成');
            } catch (error) {
                log('✗ 高德地图API测试失败: ' + error.message);
            }
        }
        
        function testMapCreation() {
            log('开始测试地图创建...');
            
            try {
                if (typeof AMap === 'undefined') {
                    log('✗ 高德地图API未加载，无法创建地图');
                    return;
                }
                
                // 创建地图实例
                map = new AMap.Map('map', {
                    zoom: 13,
                    center: [116.397428, 39.90923]
                });
                
                log('✓ 地图实例创建成功');
                
                // 监听地图加载完成事件
                map.on('complete', function() {
                    log('✓ 地图加载完成');
                });
                
                // 监听地图点击事件
                map.on('click', function(e) {
                    log('✓ 地图点击事件触发: ' + e.lnglat.getLng().toFixed(6) + ', ' + e.lnglat.getLat().toFixed(6));
                });
                
                log('地图创建测试完成');
            } catch (error) {
                log('✗ 地图创建测试失败: ' + error.message);
            }
        }
        
        function testMouseTool() {
            log('开始测试鼠标工具...');
            
            try {
                if (!map) {
                    log('✗ 请先创建地图');
                    return;
                }
                
                if (typeof AMap.MouseTool === 'undefined') {
                    log('✗ AMap.MouseTool 不可用');
                    return;
                }
                
                // 创建鼠标工具
                mouseTool = new AMap.MouseTool(map);
                log('✓ 鼠标工具创建成功');
                
                // 绑定绘制完成事件
                mouseTool.on('draw', function(e) {
                    log('✓ 绘制完成事件触发: ' + e.obj.constructor.name);
                });
                
                // 设置标记点工具
                mouseTool.marker();
                log('✓ 标记点工具设置成功');
                log('现在可以在地图上点击添加标记点');
                
                log('鼠标工具测试完成');
            } catch (error) {
                log('✗ 鼠标工具测试失败: ' + error.message);
            }
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            log('页面加载完成');
            
            // 延迟一秒后自动运行基础测试
            setTimeout(function() {
                testBasic();
                testAMapAPI();
            }, 1000);
        };
    </script>
</body>
</html>
