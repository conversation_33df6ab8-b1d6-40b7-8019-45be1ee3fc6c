<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具 - 简化版</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.Driving"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.4rem;
            margin: 0;
        }
        
        .main {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar {
            width: 300px;
            background-color: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            padding: 15px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .tool-section {
            margin-bottom: 20px;
        }
        
        .tool-section h3 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #374151;
            border-bottom: 1px solid #f3f4f6;
            padding-bottom: 5px;
        }
        
        .tool-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .tool-btn {
            padding: 10px;
            border: 2px solid #e5e7eb;
            background-color: #f9fafb;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .tool-btn:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        
        .tool-btn.active {
            background-color: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }
        
        .tool-icon {
            font-size: 1.2rem;
        }
        
        .tool-text {
            font-size: 0.8rem;
        }
        
        .snap-section {
            margin-bottom: 20px;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        
        .info-section {
            margin-bottom: 20px;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 0;
        }
        
        .stat-label {
            flex: 1;
        }
        
        .stat-value {
            font-weight: bold;
        }
        
        .map-status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            display: flex;
            gap: 15px;
        }
        
        .status-label {
            color: #6b7280;
        }
        
        .status-value {
            font-weight: bold;
        }
        
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            z-index: 1000;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .message.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🗺️ 高德地图绘制工具</h1>
            <div>
                <button id="btn-clear" class="tool-btn">清空</button>
            </div>
        </header>
        
        <div class="main">
            <div class="sidebar">
                <div class="tool-section">
                    <h3>绘制工具</h3>
                    <div class="tool-buttons">
                        <button id="btn-marker" class="tool-btn active">
                            <span class="tool-icon">📍</span>
                            <span class="tool-text">标记点</span>
                        </button>
                        <button id="btn-polyline" class="tool-btn">
                            <span class="tool-icon">📏</span>
                            <span class="tool-text">绘制线</span>
                        </button>
                        <button id="btn-polygon" class="tool-btn">
                            <span class="tool-icon">🔷</span>
                            <span class="tool-text">绘制面</span>
                        </button>
                        <button id="btn-rectangle" class="tool-btn">
                            <span class="tool-icon">⬜</span>
                            <span class="tool-text">矩形</span>
                        </button>
                        <button id="btn-circle" class="tool-btn">
                            <span class="tool-icon">⭕</span>
                            <span class="tool-text">圆形</span>
                        </button>
                        <button id="btn-edit" class="tool-btn">
                            <span class="tool-icon">✏️</span>
                            <span class="tool-text">编辑</span>
                        </button>
                    </div>
                </div>
                
                <div class="snap-section">
                    <h3>道路吸附</h3>
                    <div class="control-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="snap-enabled" checked>
                            启用道路吸附
                        </label>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3>图层信息</h3>
                    <div class="stat-item">
                        <span class="stat-label">标记点:</span>
                        <span id="marker-count" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">线条:</span>
                        <span id="line-count" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">多边形/矩形/圆形:</span>
                        <span id="polygon-count" class="stat-value">0</span>
                    </div>
                </div>
            </div>
            
            <div class="map-container">
                <div id="map"></div>
                
                <div class="map-status">
                    <div>
                        <span class="status-label">坐标:</span>
                        <span id="coordinates" class="status-value">--,--</span>
                    </div>
                    <div>
                        <span class="status-label">当前工具:</span>
                        <span id="current-tool" class="status-value">标记点</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="message" class="message"></div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 检查高德地图API是否加载
            if (typeof AMap === 'undefined') {
                alert('高德地图API未加载，请检查网络连接和API密钥');
                return;
            }
            
            // 初始化地图
            const map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923], // 北京中心
                viewMode: '2D'
            });
            
            // 初始化鼠标工具
            const mouseTool = new AMap.MouseTool(map);
            
            // 初始化编辑器
            const polyEditor = new AMap.PolyEditor(map);
            
            // 初始化路径规划服务
            const drivingService = new AMap.Driving({
                map: map,
                hideMarkers: true
            });
            
            // 状态变量
            let currentTool = 'marker';
            let isEditing = false;
            let snapEnabled = true;
            
            // 存储绘制的对象
            const overlays = {
                markers: [],
                polylines: [],
                polygons: []
            };
            
            // 显示消息
            function showMessage(text) {
                const message = document.getElementById('message');
                message.textContent = text;
                message.classList.add('show');
                
                setTimeout(() => {
                    message.classList.remove('show');
                }, 3000);
            }
            
            // 更新计数
            function updateCounts() {
                document.getElementById('marker-count').textContent = overlays.markers.length;
                document.getElementById('line-count').textContent = overlays.polylines.length;
                document.getElementById('polygon-count').textContent = overlays.polygons.length;
            }
            
            // 设置工具
            function setTool(tool) {
                // 停止编辑
                if (isEditing) {
                    polyEditor.close();
                    isEditing = false;
                }
                
                // 更新当前工具
                currentTool = tool;
                
                // 更新UI
                document.querySelectorAll('.tool-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById('btn-' + tool).classList.add('active');
                document.getElementById('current-tool').textContent = 
                    document.querySelector('#btn-' + tool + ' .tool-text').textContent;
                
                // 关闭当前工具
                mouseTool.close();
                
                // 设置新工具
                switch (tool) {
                    case 'marker':
                        mouseTool.marker();
                        break;
                    case 'polyline':
                        mouseTool.polyline({
                            strokeColor: '#FF0000',
                            strokeWeight: 3
                        });
                        break;
                    case 'polygon':
                        mouseTool.polygon({
                            strokeColor: '#FF0000',
                            strokeWeight: 3,
                            fillColor: '#FF0000',
                            fillOpacity: 0.3
                        });
                        break;
                    case 'rectangle':
                        mouseTool.rectangle({
                            strokeColor: '#FF0000',
                            strokeWeight: 3,
                            fillColor: '#FF0000',
                            fillOpacity: 0.3
                        });
                        break;
                    case 'circle':
                        mouseTool.circle({
                            strokeColor: '#FF0000',
                            strokeWeight: 3,
                            fillColor: '#FF0000',
                            fillOpacity: 0.3
                        });
                        break;
                }
                
                showMessage('已切换到' + document.getElementById('current-tool').textContent + '工具');
            }
            
            // 绑定工具按钮事件
            document.getElementById('btn-marker').addEventListener('click', () => setTool('marker'));
            document.getElementById('btn-polyline').addEventListener('click', () => setTool('polyline'));
            document.getElementById('btn-polygon').addEventListener('click', () => setTool('polygon'));
            document.getElementById('btn-rectangle').addEventListener('click', () => setTool('rectangle'));
            document.getElementById('btn-circle').addEventListener('click', () => setTool('circle'));
            document.getElementById('btn-edit').addEventListener('click', () => {
                currentTool = 'edit';
                
                document.querySelectorAll('.tool-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.getElementById('btn-edit').classList.add('active');
                document.getElementById('current-tool').textContent = '编辑';
                
                mouseTool.close();
                showMessage('点击要素进行编辑');
            });
            
            // 清空按钮
            document.getElementById('btn-clear').addEventListener('click', () => {
                if (confirm('确定要清空所有绘制内容吗？')) {
                    // 清空所有图层
                    Object.values(overlays).forEach(arr => {
                        map.remove(arr);
                        arr.length = 0;
                    });
                    
                    updateCounts();
                    showMessage('已清空所有内容');
                }
            });
            
            // 道路吸附开关
            document.getElementById('snap-enabled').addEventListener('change', (e) => {
                snapEnabled = e.target.checked;
                showMessage('道路吸附已' + (snapEnabled ? '启用' : '禁用'));
            });
            
            // 绘制完成事件
            mouseTool.on('draw', function(e) {
                const obj = e.obj;
                
                // 根据类型添加到不同集合
                if (obj instanceof AMap.Marker) {
                    overlays.markers.push(obj);
                    showMessage('标记点绘制完成');
                } else if (obj instanceof AMap.Polyline) {
                    overlays.polylines.push(obj);
                    showMessage('线条绘制完成');
                } else if (obj instanceof AMap.Polygon || 
                           obj instanceof AMap.Rectangle || 
                           obj instanceof AMap.Circle) {
                    overlays.polygons.push(obj);
                    showMessage('面绘制完成');
                }
                
                // 添加点击事件
                obj.on('click', function() {
                    if (currentTool === 'edit') {
                        // 编辑模式
                        if (obj instanceof AMap.Polyline || obj instanceof AMap.Polygon) {
                            polyEditor.setTarget(obj);
                            polyEditor.open();
                            isEditing = true;
                            showMessage('进入编辑模式，拖动节点进行编辑');
                        }
                    }
                });
                
                updateCounts();
            });
            
            // 显示鼠标坐标
            map.on('mousemove', function(e) {
                document.getElementById('coordinates').textContent = 
                    e.lnglat.getLng().toFixed(6) + ',' + e.lnglat.getLat().toFixed(6);
            });
            
            // 设置默认工具
            setTool('marker');
            
            // 地图加载完成
            map.on('complete', function() {
                showMessage('地图加载完成，可以开始绘制');
            });
        });
    </script>
</body>
</html>
