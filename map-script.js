class InteractiveMap {
    constructor() {
        this.map = null;
        this.drawnItems = null;
        this.drawControl = null;
        this.currentTool = 'marker';
        this.isEditMode = false;
        this.isDeleteMode = false;

        // 绘制状态
        this.isDrawing = false;
        this.currentPoints = [];
        this.tempLayer = null;

        // 计数器
        this.counts = {
            markers: 0,
            lines: 0,
            polygons: 0
        };

        this.init();
    }
    
    init() {
        this.initMap();
        this.initDrawControls();
        this.initEventListeners();
        this.updateCounts();
    }
    
    initMap() {
        // 初始化地图，以北京为中心
        this.map = L.map('map').setView([39.9042, 116.4074], 10);
        
        // 添加瓦片图层
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);
        
        // 创建绘制图层组
        this.drawnItems = new L.FeatureGroup();
        this.map.addLayer(this.drawnItems);
        
        // 显示鼠标坐标
        this.map.on('mousemove', (e) => {
            const lat = e.latlng.lat.toFixed(6);
            const lng = e.latlng.lng.toFixed(6);
            document.getElementById('mouse-coordinates').textContent = `${lat}, ${lng}`;
        });
        
        // 显示缩放级别
        this.map.on('zoomend', () => {
            document.getElementById('zoom-level').textContent = this.map.getZoom();
        });
        
        // 初始显示缩放级别
        document.getElementById('zoom-level').textContent = this.map.getZoom();
    }
    
    initDrawControls() {
        // 创建绘制控件
        this.drawControl = new L.Control.Draw({
            position: 'topright',
            draw: {
                polyline: false,
                polygon: false,
                circle: false,
                rectangle: false,
                marker: false,
                circlemarker: false
            },
            edit: {
                featureGroup: this.drawnItems,
                remove: true
            }
        });
        
        this.map.addControl(this.drawControl);
        
        // 隐藏默认控件
        document.querySelector('.leaflet-draw').style.display = 'none';
    }
    
    initEventListeners() {
        // 工具按钮事件
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setActiveTool(e.target.dataset.tool);
            });
        });

        // 样式控件事件
        document.getElementById('stroke-width').addEventListener('input', (e) => {
            document.getElementById('width-value').textContent = e.target.value;
        });

        document.getElementById('fill-opacity').addEventListener('input', (e) => {
            document.getElementById('opacity-value').textContent = e.target.value;
        });

        // 功能按钮事件
        document.getElementById('clear-all').addEventListener('click', () => {
            this.clearAll();
        });

        document.getElementById('export-data').addEventListener('click', () => {
            this.exportData();
        });

        document.getElementById('import-btn').addEventListener('click', () => {
            document.getElementById('import-data').click();
        });

        document.getElementById('import-data').addEventListener('change', (e) => {
            this.importData(e.target.files[0]);
        });

        // 地图绘制事件
        this.map.on('click', (e) => {
            if (this.isDeleteMode) {
                this.handleDelete(e);
            } else if (!this.isEditMode) {
                this.handleDraw(e);
            }
        });

        // 双击事件 - 完成线条或多边形绘制
        this.map.on('dblclick', (e) => {
            if (this.isDrawing && (this.currentTool === 'polyline' || this.currentTool === 'polygon')) {
                this.finishDrawing();
            }
        });

        // 键盘事件 - ESC取消绘制
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isDrawing) {
                this.cancelDrawing();
            }
        });

        // 绘制完成事件
        this.map.on(L.Draw.Event.CREATED, (e) => {
            this.drawnItems.addLayer(e.layer);
            this.updateCounts();
        });

        // 编辑事件
        this.map.on(L.Draw.Event.EDITED, () => {
            this.updateCounts();
        });

        // 删除事件
        this.map.on(L.Draw.Event.DELETED, () => {
            this.updateCounts();
        });
    }
    
    setActiveTool(tool) {
        // 更新按钮状态
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tool="${tool}"]`).classList.add('active');
        
        this.currentTool = tool;
        this.isEditMode = tool === 'edit';
        this.isDeleteMode = tool === 'delete';
        
        // 设置鼠标样式
        if (this.isDeleteMode) {
            this.map.getContainer().style.cursor = 'crosshair';
        } else {
            this.map.getContainer().style.cursor = '';
        }
    }
    
    handleDraw(e) {
        const style = this.getCurrentStyle();

        switch (this.currentTool) {
            case 'marker':
                this.addMarker(e.latlng, style);
                break;
            case 'polyline':
                this.handlePolylineDraw(e.latlng, style);
                break;
            case 'polygon':
                this.handlePolygonDraw(e.latlng, style);
                break;
            case 'rectangle':
                this.startRectangleDraw(e.latlng, style);
                break;
            case 'circle':
                this.startCircleDraw(e.latlng, style);
                break;
        }
    }

    handlePolylineDraw(latlng, style) {
        if (!this.isDrawing) {
            // 开始绘制线条
            this.isDrawing = true;
            this.currentPoints = [latlng];

            // 创建临时线条
            this.tempLayer = L.polyline(this.currentPoints, style).addTo(this.map);

            // 添加提示
            this.showDrawingTip('点击添加点，双击完成线条绘制');
        } else {
            // 添加点到线条
            this.currentPoints.push(latlng);
            this.tempLayer.setLatLngs(this.currentPoints);
        }
    }

    handlePolygonDraw(latlng, style) {
        if (!this.isDrawing) {
            // 开始绘制多边形
            this.isDrawing = true;
            this.currentPoints = [latlng];

            // 创建临时多边形
            this.tempLayer = L.polygon(this.currentPoints, style).addTo(this.map);

            // 添加提示
            this.showDrawingTip('点击添加点，双击完成多边形绘制');
        } else {
            // 添加点到多边形
            this.currentPoints.push(latlng);
            this.tempLayer.setLatLngs(this.currentPoints);
        }
    }

    startRectangleDraw(latlng, style) {
        if (!this.isDrawing) {
            this.isDrawing = true;
            this.startPoint = latlng;
            this.showDrawingTip('拖拽鼠标绘制矩形，点击完成');

            // 监听鼠标移动
            this.map.on('mousemove', this.onRectangleMouseMove.bind(this));
        } else {
            this.finishRectangleDraw(latlng, style);
        }
    }

    startCircleDraw(latlng, style) {
        if (!this.isDrawing) {
            this.isDrawing = true;
            this.startPoint = latlng;
            this.showDrawingTip('拖拽鼠标绘制圆形，点击完成');

            // 监听鼠标移动
            this.map.on('mousemove', this.onCircleMouseMove.bind(this));
        } else {
            this.finishCircleDraw(latlng, style);
        }
    }
    
    addMarker(latlng, style) {
        const marker = L.marker(latlng).addTo(this.drawnItems);
        
        // 添加弹出窗口
        const popup = L.popup()
            .setContent(`
                <div>
                    <strong>标记点</strong><br>
                    纬度: ${latlng.lat.toFixed(6)}<br>
                    经度: ${latlng.lng.toFixed(6)}<br>
                    <button onclick="this.closest('.leaflet-popup').remove()">删除</button>
                </div>
            `);
        
        marker.bindPopup(popup);
        this.updateCounts();
    }
    
    handleDelete(e) {
        // 查找点击位置的图层并删除
        this.drawnItems.eachLayer((layer) => {
            if (layer instanceof L.Marker) {
                const distance = this.map.distance(e.latlng, layer.getLatLng());
                if (distance < 50) { // 50米范围内
                    this.drawnItems.removeLayer(layer);
                    this.updateCounts();
                }
            }
        });
    }
    
    getCurrentStyle() {
        return {
            color: document.getElementById('stroke-color').value,
            fillColor: document.getElementById('fill-color').value,
            weight: parseInt(document.getElementById('stroke-width').value),
            fillOpacity: parseFloat(document.getElementById('fill-opacity').value)
        };
    }
    
    updateCounts() {
        let markers = 0, lines = 0, polygons = 0;
        
        this.drawnItems.eachLayer((layer) => {
            if (layer instanceof L.Marker) {
                markers++;
            } else if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
                lines++;
            } else if (layer instanceof L.Polygon) {
                polygons++;
            }
        });
        
        document.getElementById('marker-count').textContent = markers;
        document.getElementById('line-count').textContent = lines;
        document.getElementById('polygon-count').textContent = polygons;
    }
    
    clearAll() {
        if (confirm('确定要清空所有绘制内容吗？')) {
            this.drawnItems.clearLayers();
            this.updateCounts();
        }
    }
    
    exportData() {
        const data = this.drawnItems.toGeoJSON();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `map-data-${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                L.geoJSON(data).eachLayer((layer) => {
                    this.drawnItems.addLayer(layer);
                });
                this.updateCounts();
                alert('数据导入成功！');
            } catch (error) {
                alert('导入失败：文件格式不正确');
            }
        };
        reader.readAsText(file);
    }

    // 辅助方法
    finishDrawing() {
        if (!this.isDrawing || !this.tempLayer) return;

        // 移除临时图层
        this.map.removeLayer(this.tempLayer);

        // 创建最终图层
        const style = this.getCurrentStyle();
        let finalLayer;

        if (this.currentTool === 'polyline') {
            finalLayer = L.polyline(this.currentPoints, style);
        } else if (this.currentTool === 'polygon') {
            finalLayer = L.polygon(this.currentPoints, style);
        }

        if (finalLayer) {
            this.drawnItems.addLayer(finalLayer);
            this.addLayerPopup(finalLayer);
        }

        this.resetDrawingState();
        this.updateCounts();
        this.hideDrawingTip();
    }

    cancelDrawing() {
        if (this.tempLayer) {
            this.map.removeLayer(this.tempLayer);
        }
        this.resetDrawingState();
        this.hideDrawingTip();
    }

    resetDrawingState() {
        this.isDrawing = false;
        this.currentPoints = [];
        this.tempLayer = null;
        this.startPoint = null;
        this.map.off('mousemove');
    }

    onRectangleMouseMove(e) {
        if (!this.isDrawing || !this.startPoint) return;

        if (this.tempLayer) {
            this.map.removeLayer(this.tempLayer);
        }

        const bounds = L.latLngBounds(this.startPoint, e.latlng);
        this.tempLayer = L.rectangle(bounds, this.getCurrentStyle()).addTo(this.map);
    }

    onCircleMouseMove(e) {
        if (!this.isDrawing || !this.startPoint) return;

        if (this.tempLayer) {
            this.map.removeLayer(this.tempLayer);
        }

        const radius = this.map.distance(this.startPoint, e.latlng);
        this.tempLayer = L.circle(this.startPoint, { radius, ...this.getCurrentStyle() }).addTo(this.map);
    }

    finishRectangleDraw(latlng, style) {
        if (this.tempLayer) {
            this.map.removeLayer(this.tempLayer);
        }

        const bounds = L.latLngBounds(this.startPoint, latlng);
        const rectangle = L.rectangle(bounds, style);
        this.drawnItems.addLayer(rectangle);
        this.addLayerPopup(rectangle);

        this.resetDrawingState();
        this.updateCounts();
        this.hideDrawingTip();
    }

    finishCircleDraw(latlng, style) {
        if (this.tempLayer) {
            this.map.removeLayer(this.tempLayer);
        }

        const radius = this.map.distance(this.startPoint, latlng);
        const circle = L.circle(this.startPoint, { radius, ...style });
        this.drawnItems.addLayer(circle);
        this.addLayerPopup(circle);

        this.resetDrawingState();
        this.updateCounts();
        this.hideDrawingTip();
    }

    addLayerPopup(layer) {
        let content = '<div><strong>';

        if (layer instanceof L.Marker) {
            content += '标记点';
        } else if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
            content += '线条';
        } else if (layer instanceof L.Polygon) {
            content += '多边形';
        } else if (layer instanceof L.Rectangle) {
            content += '矩形';
        } else if (layer instanceof L.Circle) {
            content += '圆形';
        }

        content += '</strong><br><button onclick="window.mapInstance.removeLayer(this)">删除</button></div>';

        layer.bindPopup(content);
    }

    removeLayer(button) {
        const popup = button.closest('.leaflet-popup');
        if (popup && popup._source) {
            this.drawnItems.removeLayer(popup._source);
            this.updateCounts();
        }
    }

    showDrawingTip(message) {
        let tip = document.getElementById('drawing-tip');
        if (!tip) {
            tip = document.createElement('div');
            tip.id = 'drawing-tip';
            tip.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
            `;
            document.body.appendChild(tip);
        }
        tip.textContent = message;
        tip.style.display = 'block';
    }

    hideDrawingTip() {
        const tip = document.getElementById('drawing-tip');
        if (tip) {
            tip.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化地图
document.addEventListener('DOMContentLoaded', () => {
    window.mapInstance = new InteractiveMap();
});
