<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <h1>🐍 贪吃蛇游戏</h1>
        
        <div class="game-info">
            <div class="score">
                <span>得分: </span>
                <span id="score">0</span>
            </div>
            <div class="high-score">
                <span>最高分: </span>
                <span id="high-score">0</span>
            </div>
        </div>

        <div class="game-board-container">
            <canvas id="gameCanvas" width="400" height="400"></canvas>
            <div id="game-over" class="game-over hidden">
                <h2>游戏结束!</h2>
                <p>你的得分: <span id="final-score">0</span></p>
                <button id="restart-btn">重新开始</button>
            </div>
        </div>

        <div class="controls">
            <h3>游戏控制:</h3>
            <div class="control-buttons">
                <button id="up-btn" class="control-btn">↑</button>
                <div class="middle-row">
                    <button id="left-btn" class="control-btn">←</button>
                    <button id="pause-btn" class="control-btn">⏸️</button>
                    <button id="right-btn" class="control-btn">→</button>
                </div>
                <button id="down-btn" class="control-btn">↓</button>
            </div>
            <p class="instructions">
                使用方向键或点击按钮控制蛇的移动<br>
                空格键暂停/继续游戏
            </p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
