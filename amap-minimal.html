<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图最小化测试</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #container {
            padding: 20px;
        }
        
        #map {
            width: 100%;
            height: 500px;
            margin-top: 20px;
        }
        
        .buttons {
            margin-bottom: 20px;
        }
        
        button {
            padding: 10px 15px;
            margin-right: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        #status {
            margin-top: 10px;
            padding: 10px;
            background-color: #f1f1f1;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>高德地图绘制测试</h1>
        
        <div class="buttons">
            <button onclick="setMarkerTool()">绘制点</button>
            <button onclick="setPolylineTool()">绘制线</button>
            <button onclick="setPolygonTool()">绘制面</button>
            <button onclick="clearAll()">清空</button>
        </div>
        
        <div id="status">状态: 准备就绪</div>
        
        <div id="map"></div>
    </div>
    
    <script>
        // 全局变量
        var map, mouseTool;
        var markers = [], polylines = [], polygons = [];
        
        // 初始化地图
        function initMap() {
            // 创建地图实例
            map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923] // 北京中心
            });
            
            // 地图加载完成事件
            map.on('complete', function() {
                updateStatus('地图加载完成');
                initTools();
            });
        }
        
        // 初始化工具
        function initTools() {
            try {
                // 创建鼠标工具
                mouseTool = new AMap.MouseTool(map);
                
                // 绘制完成事件
                mouseTool.on('draw', function(e) {
                    onDrawComplete(e);
                });
                
                updateStatus('工具初始化完成，可以开始绘制');
            } catch (error) {
                updateStatus('工具初始化失败: ' + error.message, 'error');
                console.error('工具初始化失败:', error);
            }
        }
        
        // 设置标记点工具
        function setMarkerTool() {
            try {
                // 关闭当前工具
                mouseTool.close();
                
                // 设置标记点工具
                mouseTool.marker();
                
                updateStatus('已切换到标记点工具，点击地图添加标记');
            } catch (error) {
                updateStatus('设置标记点工具失败: ' + error.message, 'error');
                console.error('设置标记点工具失败:', error);
            }
        }
        
        // 设置线条工具
        function setPolylineTool() {
            try {
                // 关闭当前工具
                mouseTool.close();
                
                // 设置线条工具
                mouseTool.polyline({
                    strokeColor: '#FF0000',
                    strokeWeight: 3
                });
                
                updateStatus('已切换到线条工具，点击地图开始绘制，双击结束');
            } catch (error) {
                updateStatus('设置线条工具失败: ' + error.message, 'error');
                console.error('设置线条工具失败:', error);
            }
        }
        
        // 设置多边形工具
        function setPolygonTool() {
            try {
                // 关闭当前工具
                mouseTool.close();
                
                // 设置多边形工具
                mouseTool.polygon({
                    strokeColor: '#FF0000',
                    strokeWeight: 3,
                    fillColor: '#FF0000',
                    fillOpacity: 0.3
                });
                
                updateStatus('已切换到多边形工具，点击地图开始绘制，双击结束');
            } catch (error) {
                updateStatus('设置多边形工具失败: ' + error.message, 'error');
                console.error('设置多边形工具失败:', error);
            }
        }
        
        // 绘制完成事件处理
        function onDrawComplete(e) {
            try {
                var obj = e.obj;
                
                if (obj instanceof AMap.Marker) {
                    markers.push(obj);
                    updateStatus('标记点绘制完成');
                } else if (obj instanceof AMap.Polyline) {
                    polylines.push(obj);
                    updateStatus('线条绘制完成');
                } else if (obj instanceof AMap.Polygon) {
                    polygons.push(obj);
                    updateStatus('多边形绘制完成');
                }
                
                // 继续使用当前工具
                if (obj instanceof AMap.Marker) {
                    setMarkerTool();
                } else if (obj instanceof AMap.Polyline) {
                    setPolylineTool();
                } else if (obj instanceof AMap.Polygon) {
                    setPolygonTool();
                }
            } catch (error) {
                updateStatus('处理绘制完成事件失败: ' + error.message, 'error');
                console.error('处理绘制完成事件失败:', error);
            }
        }
        
        // 清空所有绘制内容
        function clearAll() {
            try {
                // 清除标记点
                for (var i = 0; i < markers.length; i++) {
                    map.remove(markers[i]);
                }
                markers = [];
                
                // 清除线条
                for (var i = 0; i < polylines.length; i++) {
                    map.remove(polylines[i]);
                }
                polylines = [];
                
                // 清除多边形
                for (var i = 0; i < polygons.length; i++) {
                    map.remove(polygons[i]);
                }
                polygons = [];
                
                updateStatus('已清空所有绘制内容');
            } catch (error) {
                updateStatus('清空内容失败: ' + error.message, 'error');
                console.error('清空内容失败:', error);
            }
        }
        
        // 更新状态信息
        function updateStatus(message, type) {
            var statusElement = document.getElementById('status');
            statusElement.textContent = '状态: ' + message;
            
            if (type === 'error') {
                statusElement.style.backgroundColor = '#ffebee';
                statusElement.style.color = '#c62828';
            } else {
                statusElement.style.backgroundColor = '#f1f1f1';
                statusElement.style.color = '#333';
            }
            
            console.log(message);
        }
        
        // 页面加载完成后初始化地图
        window.onload = function() {
            try {
                // 检查高德地图API是否加载
                if (typeof AMap === 'undefined') {
                    updateStatus('高德地图API未加载，请检查网络连接和API密钥', 'error');
                    return;
                }
                
                initMap();
            } catch (error) {
                updateStatus('初始化失败: ' + error.message, 'error');
                console.error('初始化失败:', error);
            }
        };
    </script>
</body>
</html>
