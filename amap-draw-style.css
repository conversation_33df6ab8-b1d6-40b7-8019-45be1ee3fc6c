/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    overflow: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
}

/* 顶部工具栏 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-left h1 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
}

.header-right {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #059669;
}

.btn-success.active {
    background-color: #047857;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    background-color: white;
    border-right: 1px solid #e5e7eb;
    overflow-y: auto;
    padding: 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

/* 工具区域 */
.tool-section, .snap-section, .style-section, .info-section, .edit-section {
    border-bottom: 1px solid #f3f4f6;
    padding: 20px;
}

.tool-section h3, .snap-section h3, .style-section h3, .info-section h3, .edit-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 工具按钮网格 */
.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.tool-btn {
    padding: 12px;
    border: 2px solid #e5e7eb;
    background-color: #f9fafb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
}

.tool-btn:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.tool-btn.active {
    background-color: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.tool-icon {
    font-size: 1.2rem;
}

.tool-text {
    font-size: 0.8rem;
    font-weight: 500;
}

/* 控制组 */
.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.range-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.range-control input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
    -webkit-appearance: none;
}

.range-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-control span {
    font-size: 0.8rem;
    color: #6b7280;
    min-width: 40px;
    text-align: right;
}

/* 颜色输入 */
input[type="color"] {
    width: 40px;
    height: 30px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    background: none;
}

/* 复选框样式 */
.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 统计信息 */
.info-stats {
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px dashed #e5e7eb;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-icon {
    font-size: 1rem;
}

.stat-label {
    flex: 1;
    font-size: 0.9rem;
    color: #6b7280;
}

.stat-value {
    font-weight: 600;
    color: #374151;
}

/* 选中对象信息 */
.selected-info h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #374151;
}

.details-content {
    background-color: #f9fafb;
    border-radius: 6px;
    padding: 12px;
    font-size: 0.8rem;
    line-height: 1.5;
}

.no-selection {
    color: #9ca3af;
    font-style: italic;
    margin: 0;
}

/* 编辑控制按钮 */
.edit-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

/* 地图容器 */
.map-container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

#map {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
}

/* 地图状态栏 */
.map-status {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20px;
    font-size: 0.8rem;
    z-index: 1000;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-label {
    color: #6b7280;
    font-weight: 500;
}

.status-value {
    color: #374151;
    font-weight: 600;
}

/* 道路吸附指示器 */
.snap-indicator {
    position: absolute;
    z-index: 2000;
    pointer-events: none;
    transform: translate(-50%, -100%);
}

.snap-indicator.hidden {
    display: none;
}

.snap-point {
    width: 12px;
    height: 12px;
    background-color: #10b981;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
    margin: 0 auto 4px;
    animation: pulse 1.5s infinite;
}

.snap-text {
    background-color: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

/* 绘制提示 */
.draw-tip {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2000;
    pointer-events: none;
}

.draw-tip.hidden {
    display: none;
}

.tip-content {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

/* 快捷键提示 */
.shortcut-tip {
    position: absolute;
    bottom: 60px;
    left: 10px;
    z-index: 1000;
    pointer-events: none;
}

.shortcut-content {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.shortcut-content kbd {
    background-color: #555;
    border: 1px solid #777;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: bold;
    margin: 0 2px;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.modal-content h3 {
    margin-bottom: 12px;
    color: #374151;
    font-size: 1.1rem;
}

.modal-content p {
    margin-bottom: 20px;
    color: #6b7280;
    line-height: 1.5;
}

.modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        max-height: 50vh;
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .tool-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .header {
        padding: 10px 15px;
    }
    
    .header-left h1 {
        font-size: 1.2rem;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .map-status {
        flex-direction: column;
        gap: 4px;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 4px;
    }
    
    .btn {
        padding: 4px 8px;
        font-size: 0.7rem;
    }
    
    .sidebar {
        max-height: 40vh;
    }
    
    .tool-section, .snap-section, .style-section, .info-section, .edit-section {
        padding: 15px;
    }
}
