<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式地图绘制工具</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />
    
    <link rel="stylesheet" href="map-style.css">
</head>
<body>
    <div class="app-container">
        <header class="header">
            <h1>🗺️ 交互式地图绘制工具</h1>
            <div class="header-controls">
                <button id="clear-all" class="btn btn-danger">清空所有</button>
                <button id="export-data" class="btn btn-primary">导出数据</button>
                <input type="file" id="import-data" accept=".json" style="display: none;">
                <button id="import-btn" class="btn btn-secondary">导入数据</button>
            </div>
        </header>

        <div class="main-content">
            <div class="sidebar">
                <div class="tool-section">
                    <h3>绘制工具</h3>
                    <div class="tool-buttons">
                        <button id="draw-marker" class="tool-btn active" data-tool="marker">
                            📍 标记点
                        </button>
                        <button id="draw-polyline" class="tool-btn" data-tool="polyline">
                            📏 绘制线
                        </button>
                        <button id="draw-polygon" class="tool-btn" data-tool="polygon">
                            🔷 绘制面
                        </button>
                        <button id="draw-rectangle" class="tool-btn" data-tool="rectangle">
                            ⬜ 矩形
                        </button>
                        <button id="draw-circle" class="tool-btn" data-tool="circle">
                            ⭕ 圆形
                        </button>
                        <button id="edit-mode" class="tool-btn" data-tool="edit">
                            ✏️ 编辑
                        </button>
                        <button id="delete-mode" class="tool-btn" data-tool="delete">
                            🗑️ 删除
                        </button>
                    </div>
                </div>

                <div class="info-section">
                    <h3>图层信息</h3>
                    <div id="layer-info">
                        <div class="info-item">
                            <span class="info-label">标记点:</span>
                            <span id="marker-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">线条:</span>
                            <span id="line-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">多边形:</span>
                            <span id="polygon-count">0</span>
                        </div>
                    </div>
                </div>

                <div class="style-section">
                    <h3>样式设置</h3>
                    <div class="style-controls">
                        <div class="style-group">
                            <label for="stroke-color">线条颜色:</label>
                            <input type="color" id="stroke-color" value="#3388ff">
                        </div>
                        <div class="style-group">
                            <label for="fill-color">填充颜色:</label>
                            <input type="color" id="fill-color" value="#3388ff">
                        </div>
                        <div class="style-group">
                            <label for="stroke-width">线条粗细:</label>
                            <input type="range" id="stroke-width" min="1" max="10" value="3">
                            <span id="width-value">3</span>
                        </div>
                        <div class="style-group">
                            <label for="fill-opacity">填充透明度:</label>
                            <input type="range" id="fill-opacity" min="0" max="1" step="0.1" value="0.2">
                            <span id="opacity-value">0.2</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="map-container">
                <div id="map"></div>
                <div class="map-controls">
                    <div class="coordinates">
                        <span>坐标: </span>
                        <span id="mouse-coordinates">--, --</span>
                    </div>
                    <div class="zoom-level">
                        <span>缩放: </span>
                        <span id="zoom-level">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Leaflet Draw JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    
    <script src="map-script.js"></script>
</body>
</html>
