<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.Driving"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .main {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar {
            width: 300px;
            background-color: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            padding: 20px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #374151;
            border-bottom: 1px solid #f3f4f6;
            padding-bottom: 8px;
        }
        
        .tools {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .tool-btn {
            padding: 12px;
            border: 2px solid #e5e7eb;
            background-color: #f9fafb;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            transition: all 0.2s;
        }
        
        .tool-btn:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
            transform: translateY(-2px);
        }
        
        .tool-btn.active {
            background-color: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }
        
        .tool-icon {
            font-size: 1.5rem;
        }
        
        .tool-name {
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .control-group {
            display: flex;
            align-items: center;
        }
        
        .control-group label {
            flex: 1;
            font-size: 0.9rem;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #3b82f6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .stats {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .stat-value {
            font-weight: 600;
            color: #1d4ed8;
        }
        
        .action-btn {
            padding: 10px 15px;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .action-btn:hover {
            background-color: #2563eb;
        }
        
        .action-btn.danger {
            background-color: #ef4444;
        }
        
        .action-btn.danger:hover {
            background-color: #dc2626;
        }
        
        .map-status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-size: 0.9rem;
            display: flex;
            gap: 20px;
        }
        
        .status-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            z-index: 1000;
            font-size: 0.9rem;
            opacity: 0;
            transition: opacity 0.3s;
            pointer-events: none;
        }
        
        .notification.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🗺️ 高德地图绘制工具</h1>
        </header>
        
        <div class="main">
            <div class="sidebar">
                <div class="section">
                    <h3>绘制工具</h3>
                    <div class="tools">
                        <div id="tool-marker" class="tool-btn active" onclick="setTool('marker')">
                            <div class="tool-icon">📍</div>
                            <div class="tool-name">标记点</div>
                        </div>
                        <div id="tool-polyline" class="tool-btn" onclick="setTool('polyline')">
                            <div class="tool-icon">📏</div>
                            <div class="tool-name">绘制线</div>
                        </div>
                        <div id="tool-polygon" class="tool-btn" onclick="setTool('polygon')">
                            <div class="tool-icon">🔷</div>
                            <div class="tool-name">绘制面</div>
                        </div>
                        <div id="tool-rectangle" class="tool-btn" onclick="setTool('rectangle')">
                            <div class="tool-icon">⬜</div>
                            <div class="tool-name">矩形</div>
                        </div>
                        <div id="tool-circle" class="tool-btn" onclick="setTool('circle')">
                            <div class="tool-icon">⭕</div>
                            <div class="tool-name">圆形</div>
                        </div>
                        <div id="tool-edit" class="tool-btn" onclick="setTool('edit')">
                            <div class="tool-icon">✏️</div>
                            <div class="tool-name">编辑</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>道路吸附</h3>
                    <div class="controls">
                        <div class="control-group">
                            <label>启用道路吸附</label>
                            <label class="switch">
                                <input type="checkbox" id="snap-toggle" checked onchange="toggleSnap()">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>图层信息</h3>
                    <div class="stats">
                        <div class="stat-item">
                            <div>标记点:</div>
                            <div id="marker-count" class="stat-value">0</div>
                        </div>
                        <div class="stat-item">
                            <div>线条:</div>
                            <div id="line-count" class="stat-value">0</div>
                        </div>
                        <div class="stat-item">
                            <div>面:</div>
                            <div id="polygon-count" class="stat-value">0</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>操作</h3>
                    <button class="action-btn danger" onclick="clearAll()">清空所有</button>
                </div>
            </div>
            
            <div class="map-container">
                <div id="map"></div>
                
                <div class="map-status">
                    <div>
                        <span class="status-label">坐标:</span>
                        <span id="coordinates">--,--</span>
                    </div>
                    <div>
                        <span class="status-label">当前工具:</span>
                        <span id="current-tool">标记点</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="notification" class="notification"></div>
    
    <script>
        // 全局变量
        var map, mouseTool, polyEditor;
        var currentTool = 'marker';
        var snapEnabled = true;
        var isEditing = false;
        
        // 存储绘制的对象
        var markers = [];
        var polylines = [];
        var polygons = [];
        
        // 页面加载完成后初始化
        window.onload = function() {
            initMap();
        };
        
        // 初始化地图
        function initMap() {
            // 创建地图实例
            map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923], // 北京中心
                viewMode: '2D'
            });
            
            // 地图加载完成事件
            map.on('complete', function() {
                // 初始化工具
                initTools();
                
                // 显示鼠标坐标
                map.on('mousemove', function(e) {
                    document.getElementById('coordinates').textContent = 
                        e.lnglat.getLng().toFixed(6) + ',' + e.lnglat.getLat().toFixed(6);
                });
                
                showNotification('地图加载完成，可以开始绘制');
            });
        }
        
        // 初始化工具
        function initTools() {
            // 创建鼠标工具
            mouseTool = new AMap.MouseTool(map);
            
            // 创建编辑器
            polyEditor = new AMap.PolyEditor(map);
            
            // 绘制完成事件
            mouseTool.on('draw', function(e) {
                onDrawComplete(e);
            });
            
            // 设置默认工具
            setTool('marker');
        }
        
        // 设置当前工具
        function setTool(tool) {
            // 停止编辑
            if (isEditing) {
                polyEditor.close();
                isEditing = false;
            }
            
            // 更新当前工具
            currentTool = tool;
            
            // 更新UI
            document.querySelectorAll('.tool-btn').forEach(function(btn) {
                btn.classList.remove('active');
            });
            document.getElementById('tool-' + tool).classList.add('active');
            
            // 更新状态显示
            document.getElementById('current-tool').textContent = 
                document.querySelector('#tool-' + tool + ' .tool-name').textContent;
            
            // 关闭当前工具
            mouseTool.close();
            
            // 设置新工具
            if (tool === 'edit') {
                showNotification('点击要编辑的对象');
                return;
            }
            
            // 设置绘制工具
            var options = {
                strokeColor: '#FF0000',
                strokeWeight: 3,
                fillColor: '#FF0000',
                fillOpacity: 0.3
            };
            
            switch (tool) {
                case 'marker':
                    mouseTool.marker();
                    break;
                case 'polyline':
                    mouseTool.polyline(options);
                    break;
                case 'polygon':
                    mouseTool.polygon(options);
                    break;
                case 'rectangle':
                    mouseTool.rectangle(options);
                    break;
                case 'circle':
                    mouseTool.circle(options);
                    break;
            }
            
            showNotification('已切换到' + document.getElementById('current-tool').textContent);
        }
        
        // 绘制完成事件处理
        function onDrawComplete(e) {
            var obj = e.obj;
            
            // 根据类型添加到不同集合
            if (obj instanceof AMap.Marker) {
                markers.push(obj);
                
                // 添加点击事件
                obj.on('click', function() {
                    if (currentTool === 'edit') {
                        showNotification('标记点不支持编辑');
                    }
                });
                
                showNotification('标记点绘制完成');
            } else if (obj instanceof AMap.Polyline) {
                polylines.push(obj);
                
                // 添加点击事件
                obj.on('click', function() {
                    if (currentTool === 'edit') {
                        polyEditor.setTarget(obj);
                        polyEditor.open();
                        isEditing = true;
                        showNotification('进入线条编辑模式');
                    }
                });
                
                showNotification('线条绘制完成');
            } else if (obj instanceof AMap.Polygon || 
                       obj instanceof AMap.Rectangle || 
                       obj instanceof AMap.Circle) {
                polygons.push(obj);
                
                // 添加点击事件
                obj.on('click', function() {
                    if (currentTool === 'edit') {
                        polyEditor.setTarget(obj);
                        polyEditor.open();
                        isEditing = true;
                        showNotification('进入面编辑模式');
                    }
                });
                
                showNotification('面绘制完成');
            }
            
            // 更新统计
            updateCounts();
        }
        
        // 更新统计信息
        function updateCounts() {
            document.getElementById('marker-count').textContent = markers.length;
            document.getElementById('line-count').textContent = polylines.length;
            document.getElementById('polygon-count').textContent = polygons.length;
        }
        
        // 切换道路吸附
        function toggleSnap() {
            snapEnabled = document.getElementById('snap-toggle').checked;
            showNotification('道路吸附已' + (snapEnabled ? '启用' : '禁用'));
        }
        
        // 清空所有
        function clearAll() {
            if (confirm('确定要清空所有绘制内容吗？')) {
                // 清除标记点
                map.remove(markers);
                markers = [];
                
                // 清除线条
                map.remove(polylines);
                polylines = [];
                
                // 清除面
                map.remove(polygons);
                polygons = [];
                
                // 更新统计
                updateCounts();
                
                showNotification('已清空所有内容');
            }
        }
        
        // 显示通知
        function showNotification(message) {
            var notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(function() {
                notification.classList.remove('show');
            }, 3000);
            
            console.log(message);
        }
    </script>
</body>
</html>
