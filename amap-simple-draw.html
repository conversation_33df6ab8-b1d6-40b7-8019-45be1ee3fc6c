<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图简化绘制工具</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.CircleEditor,AMap.RectangleEditor,AMap.Driving"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        
        .header {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar {
            width: 250px;
            background-color: white;
            border-right: 1px solid #e5e7eb;
            padding: 15px;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .tool-section {
            margin-bottom: 20px;
        }
        
        .tool-section h3 {
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .tool-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .tool-btn {
            padding: 10px;
            border: 1px solid #e5e7eb;
            background-color: #f9fafb;
            border-radius: 4px;
            cursor: pointer;
            text-align: center;
        }
        
        .tool-btn:hover {
            background-color: #f3f4f6;
        }
        
        .tool-btn.active {
            background-color: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }
        
        .status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(255,255,255,0.8);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>高德地图绘制工具</h1>
    </div>
    
    <div class="main-content">
        <div class="sidebar">
            <div class="tool-section">
                <h3>绘制工具</h3>
                <div class="tool-buttons">
                    <button id="draw-marker" class="tool-btn active" data-tool="marker">标记点</button>
                    <button id="draw-polyline" class="tool-btn" data-tool="polyline">绘制线</button>
                    <button id="draw-polygon" class="tool-btn" data-tool="polygon">绘制面</button>
                    <button id="draw-rectangle" class="tool-btn" data-tool="rectangle">矩形</button>
                    <button id="draw-circle" class="tool-btn" data-tool="circle">圆形</button>
                    <button id="select-mode" class="tool-btn" data-tool="select">选择</button>
                </div>
            </div>
            
            <div class="tool-section">
                <h3>操作</h3>
                <button id="clear-all" style="width: 100%; padding: 8px; margin-top: 5px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">清空所有</button>
            </div>
            
            <div id="debug-info" style="margin-top: 20px; font-size: 12px; color: #666;"></div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            <div class="status">
                <span id="current-tool">当前工具: 标记点</span>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，初始化应用...');
            
            // 初始化地图
            const map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923]
            });
            
            // 初始化鼠标工具
            const mouseTool = new AMap.MouseTool(map);
            
            // 当前工具
            let currentTool = 'marker';
            
            // 绘制的图形集合
            const overlays = {
                markers: [],
                polylines: [],
                polygons: [],
                circles: [],
                rectangles: []
            };
            
            // 调试信息
            function updateDebugInfo(message) {
                const debugElement = document.getElementById('debug-info');
                debugElement.innerHTML += `<div>${message}</div>`;
                console.log(message);
            }
            
            // 设置活动工具
            function setActiveTool(tool) {
                updateDebugInfo(`设置工具: ${tool}`);
                
                // 更新当前工具
                currentTool = tool;
                
                // 更新UI
                document.querySelectorAll('.tool-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.tool === tool);
                });
                
                document.getElementById('current-tool').textContent = `当前工具: ${document.querySelector(`.tool-btn[data-tool="${tool}"]`).textContent}`;
                
                // 关闭当前鼠标工具
                mouseTool.close();
                
                // 根据工具类型设置
                switch (tool) {
                    case 'marker':
                        mouseTool.marker();
                        break;
                    case 'polyline':
                        mouseTool.polyline();
                        break;
                    case 'polygon':
                        mouseTool.polygon();
                        break;
                    case 'rectangle':
                        mouseTool.rectangle();
                        break;
                    case 'circle':
                        mouseTool.circle();
                        break;
                    case 'select':
                        // 选择模式不需要激活鼠标工具
                        break;
                }
            }
            
            // 绑定工具按钮点击事件
            document.querySelectorAll('.tool-btn').forEach(btn => {
                updateDebugInfo(`添加事件监听器到按钮: ${btn.dataset.tool}`);
                
                btn.addEventListener('click', function(e) {
                    const tool = this.dataset.tool;
                    updateDebugInfo(`按钮点击: ${tool}`);
                    setActiveTool(tool);
                });
            });
            
            // 清空所有按钮
            document.getElementById('clear-all').addEventListener('click', function() {
                updateDebugInfo('清空所有图形');
                
                // 清除所有覆盖物
                Object.values(overlays).forEach(array => {
                    array.forEach(overlay => {
                        map.remove(overlay);
                    });
                    array.length = 0;
                });
            });
            
            // 监听绘制完成事件
            mouseTool.on('draw', function(e) {
                const overlay = e.obj;
                
                if (e.type === 'marker') {
                    overlays.markers.push(overlay);
                } else if (e.type === 'polyline') {
                    overlays.polylines.push(overlay);
                } else if (e.type === 'polygon') {
                    overlays.polygons.push(overlay);
                } else if (e.type === 'rectangle') {
                    overlays.rectangles.push(overlay);
                } else if (e.type === 'circle') {
                    overlays.circles.push(overlay);
                }
                
                updateDebugInfo(`绘制完成: ${e.type}`);
            });
            
            // 设置默认工具
            setActiveTool('marker');
            
            updateDebugInfo('应用初始化完成');
        });
    </script>
</body>
</html>
