<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.Driving"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0;
        }
        
        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .sidebar {
            width: 280px;
            background-color: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            padding: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
        }
        
        .tool-section {
            border-bottom: 1px solid #f3f4f6;
            padding: 15px;
        }
        
        .tool-section h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #374151;
        }
        
        .tool-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .tool-btn {
            padding: 10px;
            border: 2px solid #e5e7eb;
            background-color: #f9fafb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }
        
        .tool-btn:hover {
            background-color: #f3f4f6;
            border-color: #d1d5db;
        }
        
        .tool-btn.active {
            background-color: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }
        
        .tool-icon {
            font-size: 1.2rem;
        }
        
        .tool-text {
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .control-group {
            margin-bottom: 12px;
        }
        
        .control-group label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
        
        .toggle-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toggle-text {
            font-size: 0.9rem;
            color: #374151;
        }
        
        .map-container {
            flex: 1;
            position: relative;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .map-status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            font-size: 0.8rem;
            z-index: 100;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: 4px;
        }
        
        .status-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .status-value {
            color: #374151;
            font-weight: 600;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .draw-tip {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9rem;
            z-index: 100;
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ 高德地图绘制工具</h1>
        <div>
            <button id="clear-all" class="btn btn-danger">🗑️ 清空所有</button>
        </div>
    </div>
    
    <div class="main-content">
        <div class="sidebar">
            <div class="tool-section">
                <h3>绘制工具</h3>
                <div class="tool-buttons">
                    <button class="tool-btn active" data-tool="marker">
                        <span class="tool-icon">📍</span>
                        <span class="tool-text">标记点</span>
                    </button>
                    <button class="tool-btn" data-tool="polyline">
                        <span class="tool-icon">📏</span>
                        <span class="tool-text">绘制线</span>
                    </button>
                    <button class="tool-btn" data-tool="polygon">
                        <span class="tool-icon">🔷</span>
                        <span class="tool-text">绘制面</span>
                    </button>
                    <button class="tool-btn" data-tool="rectangle">
                        <span class="tool-icon">⬜</span>
                        <span class="tool-text">矩形</span>
                    </button>
                    <button class="tool-btn" data-tool="circle">
                        <span class="tool-icon">⭕</span>
                        <span class="tool-text">圆形</span>
                    </button>
                    <button class="tool-btn" data-tool="select">
                        <span class="tool-icon">👆</span>
                        <span class="tool-text">选择</span>
                    </button>
                </div>
            </div>
            
            <div class="tool-section">
                <h3>道路吸附设置</h3>
                <div class="control-group">
                    <div class="toggle-label">
                        <span class="toggle-text">道路吸附</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="road-snap" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
                <div class="control-group">
                    <label for="snap-distance">吸附距离</label>
                    <input type="range" id="snap-distance" min="10" max="200" value="50" style="width: 100%">
                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #6b7280;">
                        <span>10m</span>
                        <span id="distance-value">50m</span>
                        <span>200m</span>
                    </div>
                </div>
            </div>
            
            <div class="tool-section">
                <h3>使用说明</h3>
                <ul style="font-size: 0.9rem; color: #4b5563; margin-left: 20px; line-height: 1.5;">
                    <li>选择绘制工具后点击地图开始绘制</li>
                    <li>线条和多边形：双击结束绘制</li>
                    <li>矩形和圆形：点击拖动绘制</li>
                    <li>按 ESC 键取消当前绘制</li>
                    <li>开启道路吸附后，线条会自动吸附到道路上</li>
                </ul>
            </div>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
            
            <div class="map-status">
                <div class="status-item">
                    <span class="status-label">坐标:</span>
                    <span id="mouse-coordinates" class="status-value">--, --</span>
                </div>
                <div class="status-item">
                    <span class="status-label">当前工具:</span>
                    <span id="current-tool" class="status-value">标记点</span>
                </div>
                <div class="status-item">
                    <span class="status-label">道路吸附:</span>
                    <span id="snap-status" class="status-value">已开启</span>
                </div>
            </div>
            
            <div id="draw-tip" class="draw-tip">点击地图开始绘制</div>
        </div>
    </div>
    
    <script src="amap-draw-tool.js"></script>
</body>
</html>
