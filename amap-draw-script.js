/**
 * 高德地图绘制工具
 * 支持点、线、面的绘制，道路自动吸附，以及完整的编辑功能
 */

class AMapDrawingTool {
    constructor() {
        // 地图实例
        this.map = null;
        
        // 绘制工具
        this.mouseTool = null;
        
        // 编辑器实例
        this.polyEditor = null;
        this.circleEditor = null;
        this.rectangleEditor = null;
        
        // 路径规划服务
        this.drivingService = null;
        
        // 当前工具
        this.currentTool = 'marker';
        
        // 绘制的图形集合
        this.overlays = {
            markers: [],
            polylines: [],
            polygons: [],
            circles: [],
            rectangles: []
        };
        
        // 选中的图形
        this.selectedOverlay = null;
        this.selectedType = null;
        
        // 复制的图形
        this.copiedOverlay = null;
        this.copiedType = null;
        
        // 道路吸附设置
        this.snapSettings = {
            enabled: true,
            distance: 50,
            snapToRoads: true,
            autoRoute: true
        };
        
        // 样式设置
        this.styleSettings = {
            strokeColor: '#ff4444',
            fillColor: '#ff4444',
            strokeWidth: 3,
            fillOpacity: 0.3
        };
        
        // 操作历史
        this.history = {
            actions: [],
            currentIndex: -1,
            maxSize: 20
        };
        
        // 绘制状态
        this.drawingState = {
            isDrawing: false,
            tempPoints: [],
            tempMarkers: [],
            tempPolyline: null
        };
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化地图和工具
     */
    init() {
        console.log('开始初始化应用...');

        try {
            // 检查高德地图API是否加载
            if (typeof AMap === 'undefined') {
                throw new Error('高德地图API未加载，请检查网络连接和API密钥');
            }

            // 初始化地图
            this.initMap();

            // 地图加载完成后再初始化其他组件
            this.map.on('complete', () => {
                console.log('地图加载完成，初始化其他组件');

                // 初始化工具和事件
                this.initTools();
                this.initEventListeners();
                this.updateCounts();
                this.updateUIState();

                // 设置默认工具
                this.setActiveTool('marker');

                console.log('应用初始化完成');

                // 检查工具按钮是否存在
                const toolButtons = document.querySelectorAll('.tool-btn');
                console.log('找到工具按钮数量:', toolButtons.length);
                toolButtons.forEach(btn => {
                    console.log('工具按钮:', btn.dataset.tool, btn.textContent.trim());
                });

                this.showMessage('地图绘制工具已准备就绪', 'success');
            });
        } catch (error) {
            console.error('初始化失败:', error);
            this.showMessage('初始化失败: ' + error.message, 'error');
        }
    }
    
    /**
     * 初始化高德地图
     */
    initMap() {
        console.log('开始初始化地图...');

        // 创建地图实例
        this.map = new AMap.Map('map', {
            zoom: 13,
            center: [116.397428, 39.90923], // 默认北京中心
            viewMode: '2D',
            resizeEnable: true,
            rotateEnable: false,
            pitchEnable: false,
            mapStyle: 'amap://styles/normal',
            features: ['bg', 'road', 'building', 'point']
        });

        console.log('地图实例创建完成:', this.map);

        // 等待地图加载完成
        this.map.on('complete', () => {
            console.log('地图加载完成');

            // 添加地图控件
            this.map.addControl(new AMap.Scale());
            this.map.addControl(new AMap.ToolBar({
                position: 'RB'
            }));

            // 显示鼠标坐标
            this.map.on('mousemove', (e) => {
                const lng = e.lnglat.getLng().toFixed(6);
                const lat = e.lnglat.getLat().toFixed(6);
                document.getElementById('mouse-coordinates').textContent = `${lng}, ${lat}`;
            });

            // 显示缩放级别
            this.map.on('zoomchange', () => {
                document.getElementById('zoom-level').textContent = this.map.getZoom().toFixed(1);
            });

            // 初始显示缩放级别
            document.getElementById('zoom-level').textContent = this.map.getZoom().toFixed(1);
        });
    }
    
    /**
     * 初始化绘制工具和服务
     */
    initTools() {
        console.log('开始初始化工具...');

        try {
            // 初始化鼠标工具
            this.mouseTool = new AMap.MouseTool(this.map);
            console.log('鼠标工具初始化完成:', this.mouseTool);

            // 初始化编辑器
            this.polyEditor = new AMap.PolyEditor(this.map);
            this.circleEditor = new AMap.CircleEditor(this.map);
            this.rectangleEditor = new AMap.RectangleEditor(this.map);
            console.log('编辑器初始化完成');

            // 初始化路径规划服务
            this.drivingService = new AMap.Driving({
                map: this.map,
                hideMarkers: true,
                isOutline: false,
                showTraffic: false,
                autoFitView: false
            });
            console.log('路径规划服务初始化完成');
        } catch (error) {
            console.error('工具初始化失败:', error);
            this.showMessage('工具初始化失败，请检查控制台错误信息', 'error');
        }
    }
    
    /**
     * 初始化事件监听
     */
    initEventListeners() {
        console.log('初始化事件监听...');

        try {
            // 工具按钮点击事件
            const toolButtons = document.querySelectorAll('.tool-btn');
            console.log('找到工具按钮数量:', toolButtons.length);

            if (toolButtons.length === 0) {
                console.error('未找到工具按钮，请检查HTML结构');
                this.showMessage('未找到工具按钮，请检查页面结构', 'error');
                return;
            }

            toolButtons.forEach((btn, index) => {
                console.log(`添加事件监听器到按钮 ${index + 1}:`, btn.dataset.tool, btn.textContent.trim());

                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('工具按钮被点击:', e.currentTarget.dataset.tool);
                    const tool = e.currentTarget.dataset.tool;
                    if (tool) {
                        this.setActiveTool(tool);
                    } else {
                        console.error('按钮缺少data-tool属性');
                    }
                });
            });

            // 样式控件事件
            document.getElementById('stroke-color').addEventListener('input', (e) => {
                this.styleSettings.strokeColor = e.target.value;
                this.updateSelectedStyle();
            });

            document.getElementById('fill-color').addEventListener('input', (e) => {
                this.styleSettings.fillColor = e.target.value;
                this.updateSelectedStyle();
            });

            document.getElementById('stroke-width').addEventListener('input', (e) => {
                this.styleSettings.strokeWidth = parseInt(e.target.value);
                document.getElementById('width-value').textContent = `${e.target.value}px`;
                this.updateSelectedStyle();
            });

            document.getElementById('fill-opacity').addEventListener('input', (e) => {
                this.styleSettings.fillOpacity = parseFloat(e.target.value);
                document.getElementById('opacity-value').textContent = e.target.value;
                this.updateSelectedStyle();
            });

            // 道路吸附设置
            document.getElementById('toggle-snap').addEventListener('click', (e) => {
                this.snapSettings.enabled = !this.snapSettings.enabled;
                e.target.textContent = `🧲 道路吸附: ${this.snapSettings.enabled ? '开' : '关'}`;
                e.target.classList.toggle('active', this.snapSettings.enabled);
                e.target.dataset.snap = this.snapSettings.enabled;
            });

            document.getElementById('snap-distance').addEventListener('input', (e) => {
                this.snapSettings.distance = parseInt(e.target.value);
                document.getElementById('distance-value').textContent = `${e.target.value}m`;
            });

            document.getElementById('snap-to-roads').addEventListener('change', (e) => {
                this.snapSettings.snapToRoads = e.target.checked;
            });

            document.getElementById('auto-route').addEventListener('change', (e) => {
                this.snapSettings.autoRoute = e.target.checked;
            });

            // 编辑操作按钮
            document.getElementById('delete-selected').addEventListener('click', () => {
                this.showConfirmDialog('删除选中对象', '确定要删除选中的对象吗？', () => {
                    this.deleteSelected();
                });
            });

            document.getElementById('copy-selected').addEventListener('click', () => {
                this.copySelected();
            });

            document.getElementById('paste-object').addEventListener('click', () => {
                this.pasteObject();
            });

            document.getElementById('edit-selected').addEventListener('click', () => {
                this.editSelected();
            });

            // 功能按钮
            document.getElementById('clear-all').addEventListener('click', () => {
                this.showConfirmDialog('清空所有对象', '确定要清空所有绘制的对象吗？', () => {
                    this.clearAll();
                });
            });

            document.getElementById('export-data').addEventListener('click', () => {
                this.exportData();
            });

            document.getElementById('import-btn').addEventListener('click', () => {
                document.getElementById('import-data').click();
            });

            document.getElementById('import-data').addEventListener('change', (e) => {
                this.importData(e.target.files[0]);
            });

            // 撤销/重做
            document.getElementById('undo-action').addEventListener('click', () => {
                this.undo();
            });

            document.getElementById('redo-action').addEventListener('click', () => {
                this.redo();
            });

            // 键盘事件 - ESC键结束绘制
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    console.log('按下ESC键，结束当前绘制');
                    this.cancelCurrentDraw();
                }
            });

            // 确认对话框
            document.getElementById('confirm-yes').addEventListener('click', () => {
                if (this.confirmCallback) {
                    this.confirmCallback();
                    this.confirmCallback = null;
                }
                this.hideConfirmDialog();
            });

            document.getElementById('confirm-no').addEventListener('click', () => {
                this.hideConfirmDialog();
            });

            console.log('UI事件监听初始化完成');

            // 地图点击事件 - 用于选择模式
            this.map.on('click', (e) => {
                console.log('地图点击事件:', e, '当前工具:', this.currentTool);
                if (this.currentTool === 'select') {
                    this.handleMapClick(e);
                }
            });

            // 鼠标工具事件 - 这是关键部分
            if (this.mouseTool) {
                console.log('绑定鼠标工具绘制完成事件');

                // 使用bind确保回调函数中的this指向正确
                const boundHandler = this.handleDrawComplete.bind(this);

                // 移除可能存在的旧事件监听
                this.mouseTool.off('draw');

                // 添加新的事件监听
                this.mouseTool.on('draw', function(e) {
                    console.log('绘制完成事件触发:', e);
                    boundHandler(e);
                });
            } else {
                console.error('鼠标工具未初始化，无法绑定事件');
            }

            console.log('所有事件监听初始化完成');
        } catch (error) {
            console.error('初始化事件监听失败:', error);
        }
    }
    
    /**
     * 设置当前活动工具
     * @param {string} tool - 工具名称
     */
    setActiveTool(tool) {
        console.log('设置活动工具:', tool);

        try {
            if (!tool) {
                console.error('工具名称为空');
                return;
            }

            // 停止当前编辑
            this.stopEditing();

            // 更新当前工具
            this.currentTool = tool;

            // 更新UI
            const toolButtons = document.querySelectorAll('.tool-btn');
            console.log('更新工具按钮状态，找到按钮数量:', toolButtons.length);

            toolButtons.forEach(btn => {
                const isActive = btn.dataset.tool === tool;
                btn.classList.toggle('active', isActive);
                console.log(`按钮 ${btn.dataset.tool}: ${isActive ? '激活' : '未激活'}`);
            });

            const toolTextElement = document.querySelector(`.tool-btn[data-tool="${tool}"] .tool-text`);
            if (toolTextElement) {
                document.getElementById('current-tool-name').textContent = toolTextElement.textContent;
                console.log('更新工具名称显示:', toolTextElement.textContent);
            } else {
                console.warn('未找到工具文本元素:', tool);
            }

            // 根据工具类型设置鼠标工具
            this.setMouseTool(tool);

            console.log('工具设置完成:', tool);
        } catch (error) {
            console.error('设置工具失败:', error);
            this.showMessage('设置工具失败: ' + error.message, 'error');
        }
    }
    
    /**
     * 取消当前绘制
     */
    cancelCurrentDraw() {
        console.log('取消当前绘制');

        try {
            // 关闭鼠标工具
            if (this.mouseTool) {
                this.mouseTool.close();
            }

            // 如果正在进行自定义线条绘制，移除事件监听
            if (this.drawingState.isDrawing && this.currentTool === 'polyline') {
                this.map.off('click', this.handlePolylineClick);
                this.map.off('dblclick', this.handlePolylineDoubleClick);
                this.drawingState.isDrawing = false;
            }

            // 清除临时绘制状态
            this.clearDrawingState();

            // 隐藏绘制提示
            this.hideDrawTip();

            // 切换到选择模式
            this.setActiveTool('select');

            this.showMessage('已取消绘制', 'info');
        } catch (error) {
            console.error('取消绘制失败:', error);
        }
    }

    /**
     * 设置鼠标工具
     * @param {string} tool - 工具名称
     */
    setMouseTool(tool) {
        console.log('设置鼠标工具:', tool);

        try {
            // 关闭当前鼠标工具
            this.mouseTool.close();

            // 清除临时绘制状态
            this.clearDrawingState();

            // 根据工具类型设置
            switch (tool) {
                case 'marker':
                    console.log('启用标记点工具');
                    this.mouseTool.marker({
                        icon: new AMap.Icon({
                            size: new AMap.Size(25, 34),
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                        })
                    });
                    this.showDrawTip('点击地图添加标记点，按ESC取消');
                    break;

                case 'polyline':
                    console.log('启用线条工具, 吸附状态:', this.snapSettings.enabled);
                    if (this.snapSettings.enabled && this.snapSettings.autoRoute) {
                        // 使用自定义绘制方法进行道路吸附
                        this.startCustomPolylineDraw();
                    } else {
                        this.mouseTool.polyline({
                            strokeColor: this.styleSettings.strokeColor,
                            strokeWeight: this.styleSettings.strokeWidth
                        });
                        this.showDrawTip('点击地图开始绘制线条，双击结束，按ESC取消');
                    }
                    break;

                case 'polygon':
                    console.log('启用多边形工具');
                    this.mouseTool.polygon({
                        strokeColor: this.styleSettings.strokeColor,
                        strokeWeight: this.styleSettings.strokeWidth,
                        fillColor: this.styleSettings.fillColor,
                        fillOpacity: this.styleSettings.fillOpacity
                    });
                    this.showDrawTip('点击地图开始绘制多边形，双击结束，按ESC取消');
                    break;

                case 'rectangle':
                    console.log('启用矩形工具');
                    this.mouseTool.rectangle({
                        strokeColor: this.styleSettings.strokeColor,
                        strokeWeight: this.styleSettings.strokeWidth,
                        fillColor: this.styleSettings.fillColor,
                        fillOpacity: this.styleSettings.fillOpacity
                    });
                    this.showDrawTip('点击并拖动绘制矩形，按ESC取消');
                    break;

                case 'circle':
                    console.log('启用圆形工具');
                    this.mouseTool.circle({
                        strokeColor: this.styleSettings.strokeColor,
                        strokeWeight: this.styleSettings.strokeWidth,
                        fillColor: this.styleSettings.fillColor,
                        fillOpacity: this.styleSettings.fillOpacity
                    });
                    this.showDrawTip('点击并拖动绘制圆形，按ESC取消');
                    break;

                case 'select':
                    console.log('启用选择工具');
                    this.showDrawTip('点击选择已绘制的对象');
                    break;
            }
        } catch (error) {
            console.error('设置鼠标工具失败:', error);
            this.showMessage('设置绘制工具失败，请检查控制台错误信息', 'error');
        }
    }

    /**
     * 开始自定义线条绘制（支持道路吸附）
     */
    startCustomPolylineDraw() {
        this.drawingState.isDrawing = true;
        this.drawingState.tempPoints = [];
        this.drawingState.tempMarkers = [];
        this.drawingState.tempPolyline = null;

        this.showDrawTip('点击地图开始绘制路径，双击结束（支持道路吸附）');

        // 监听地图点击事件
        this.map.on('click', this.handlePolylineClick.bind(this));
        this.map.on('dblclick', this.handlePolylineDoubleClick.bind(this));
    }

    /**
     * 处理线条绘制点击事件
     */
    handlePolylineClick(e) {
        if (!this.drawingState.isDrawing || this.currentTool !== 'polyline') return;

        const point = [e.lnglat.getLng(), e.lnglat.getLat()];
        this.drawingState.tempPoints.push(point);

        // 添加临时标记
        const marker = new AMap.Marker({
            position: point,
            icon: new AMap.Icon({
                size: new AMap.Size(8, 8),
                image: 'data:image/svg+xml;base64,' + btoa(`
                    <svg width="8" height="8" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="4" cy="4" r="3" fill="${this.styleSettings.strokeColor}" stroke="white" stroke-width="1"/>
                    </svg>
                `)
            })
        });

        this.map.add(marker);
        this.drawingState.tempMarkers.push(marker);

        // 如果有两个或更多点，进行路径规划
        if (this.drawingState.tempPoints.length >= 2 && this.snapSettings.autoRoute) {
            this.planRoute();
        } else if (this.drawingState.tempPoints.length >= 2) {
            // 直接连线
            this.updateTempPolyline();
        }
    }

    /**
     * 处理线条绘制双击事件
     */
    handlePolylineDoubleClick(e) {
        if (!this.drawingState.isDrawing || this.currentTool !== 'polyline') return;

        e.preventDefault();
        this.finishPolylineDraw();
    }

    /**
     * 路径规划
     */
    planRoute() {
        if (this.drawingState.tempPoints.length < 2) return;

        const points = this.drawingState.tempPoints;
        const startPoint = points[points.length - 2];
        const endPoint = points[points.length - 1];

        this.drivingService.search(
            new AMap.LngLat(startPoint[0], startPoint[1]),
            new AMap.LngLat(endPoint[0], endPoint[1]),
            (status, result) => {
                if (status === 'complete' && result.routes && result.routes.length > 0) {
                    const route = result.routes[0];
                    const routePath = [];

                    // 提取路径点
                    route.steps.forEach(step => {
                        step.path.forEach(point => {
                            routePath.push([point.getLng(), point.getLat()]);
                        });
                    });

                    // 更新临时路径
                    if (routePath.length > 0) {
                        // 替换最后两个点之间的直线为规划路径
                        const newPath = [...this.drawingState.tempPoints.slice(0, -2), ...routePath];
                        this.updateTempPolyline(newPath);
                    }
                } else {
                    // 路径规划失败，使用直线连接
                    this.updateTempPolyline();
                }
            }
        );
    }

    /**
     * 更新临时线条
     */
    updateTempPolyline(customPath = null) {
        // 移除旧的临时线条
        if (this.drawingState.tempPolyline) {
            this.map.remove(this.drawingState.tempPolyline);
        }

        const path = customPath || this.drawingState.tempPoints;

        if (path.length >= 2) {
            this.drawingState.tempPolyline = new AMap.Polyline({
                path: path,
                strokeColor: this.styleSettings.strokeColor,
                strokeWeight: this.styleSettings.strokeWidth,
                strokeOpacity: 0.8,
                strokeStyle: 'dashed'
            });

            this.map.add(this.drawingState.tempPolyline);
        }
    }

    /**
     * 完成线条绘制
     */
    finishPolylineDraw() {
        if (this.drawingState.tempPoints.length < 2) {
            this.clearDrawingState();
            return;
        }

        // 创建最终的线条
        let finalPath = this.drawingState.tempPoints;

        // 如果启用了路径规划且有临时线条，使用其路径
        if (this.drawingState.tempPolyline && this.snapSettings.autoRoute) {
            finalPath = this.drawingState.tempPolyline.getPath().map(point => [point.getLng(), point.getLat()]);
        }

        const polyline = new AMap.Polyline({
            path: finalPath,
            strokeColor: this.styleSettings.strokeColor,
            strokeWeight: this.styleSettings.strokeWidth,
            strokeOpacity: 1,
            cursor: 'pointer'
        });

        // 添加到地图和集合
        this.map.add(polyline);
        this.overlays.polylines.push(polyline);

        // 添加点击事件
        polyline.on('click', (e) => {
            this.selectOverlay(polyline, 'polyline');
        });

        // 记录操作历史
        this.addToHistory('add', 'polyline', polyline);

        // 清理绘制状态
        this.clearDrawingState();

        // 更新统计
        this.updateCounts();
        this.hideDrawTip();

        // 移除事件监听
        this.map.off('click', this.handlePolylineClick);
        this.map.off('dblclick', this.handlePolylineDoubleClick);
    }

    /**
     * 清除绘制状态
     */
    clearDrawingState() {
        // 清除临时标记
        if (this.drawingState.tempMarkers.length > 0) {
            this.map.remove(this.drawingState.tempMarkers);
            this.drawingState.tempMarkers = [];
        }

        // 清除临时线条
        if (this.drawingState.tempPolyline) {
            this.map.remove(this.drawingState.tempPolyline);
            this.drawingState.tempPolyline = null;
        }

        // 重置状态
        this.drawingState.isDrawing = false;
        this.drawingState.tempPoints = [];
    }

    /**
     * 处理绘制完成事件
     */
    handleDrawComplete(e) {
        console.log('绘制完成事件:', e);

        try {
            if (!e || !e.obj) {
                console.error('绘制事件缺少对象数据');
                return;
            }

            const overlay = e.obj;
            const type = this.getOverlayType(overlay);

            console.log('绘制的对象类型:', type, overlay);

            if (!type) {
                console.error('无法识别的对象类型');
                return;
            }

            // 添加到对应集合
            if (!this.overlays[type + 's']) {
                console.error('未找到对应的集合:', type + 's');
                return;
            }

            this.overlays[type + 's'].push(overlay);
            console.log('已添加到集合:', type + 's', this.overlays[type + 's'].length);

            // 添加点击事件
            overlay.on('click', (clickEvent) => {
                console.log('点击了绘制的对象:', type, clickEvent);
                this.selectOverlay(overlay, type);
            });

            // 记录操作历史
            this.addToHistory('add', type, overlay);

            // 更新统计
            this.updateCounts();
            this.hideDrawTip();

            // 显示成功消息
            this.showMessage(`${this.getTypeDisplayName(type)}绘制完成`, 'success');

            // 对于标记点，重新激活工具以便继续绘制
            // 对于其他图形，不自动重新激活，让用户手动选择
            if (type === 'marker') {
                setTimeout(() => {
                    this.setMouseTool(this.currentTool);
                }, 100);
            } else {
                // 切换到选择模式
                setTimeout(() => {
                    this.setActiveTool('select');
                }, 100);
            }
        } catch (error) {
            console.error('处理绘制完成事件失败:', error);
            this.showMessage('处理绘制失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取覆盖物类型
     */
    getOverlayType(overlay) {
        if (overlay instanceof AMap.Marker) return 'marker';
        if (overlay instanceof AMap.Polyline) return 'polyline';
        if (overlay instanceof AMap.Polygon) return 'polygon';
        if (overlay instanceof AMap.Circle) return 'circle';
        if (overlay instanceof AMap.Rectangle) return 'rectangle';
        return null;
    }

    /**
     * 处理地图点击事件（选择模式）
     */
    handleMapClick(e) {
        // 清除当前选择
        this.clearSelection();
    }

    /**
     * 选择覆盖物
     */
    selectOverlay(overlay, type) {
        // 清除之前的选择
        this.clearSelection();

        // 设置新选择
        this.selectedOverlay = overlay;
        this.selectedType = type;

        // 高亮显示
        this.highlightOverlay(overlay, type);

        // 更新UI
        this.updateSelectedInfo();
        this.updateUIState();
    }

    /**
     * 高亮覆盖物
     */
    highlightOverlay(overlay, type) {
        const highlightStyle = {
            strokeColor: '#00ff00',
            strokeWeight: this.styleSettings.strokeWidth + 2,
            fillColor: '#00ff00',
            fillOpacity: 0.1
        };

        switch (type) {
            case 'marker':
                // 标记点高亮通过添加圆圈实现
                this.highlightCircle = new AMap.Circle({
                    center: overlay.getPosition(),
                    radius: 20,
                    strokeColor: '#00ff00',
                    strokeWeight: 2,
                    fillColor: '#00ff00',
                    fillOpacity: 0.2
                });
                this.map.add(this.highlightCircle);
                break;

            case 'polyline':
                overlay.setOptions({
                    strokeColor: highlightStyle.strokeColor,
                    strokeWeight: highlightStyle.strokeWeight
                });
                break;

            case 'polygon':
                overlay.setOptions(highlightStyle);
                break;

            case 'circle':
                overlay.setOptions(highlightStyle);
                break;

            case 'rectangle':
                overlay.setOptions(highlightStyle);
                break;
        }
    }

    /**
     * 清除选择
     */
    clearSelection() {
        if (this.selectedOverlay && this.selectedType) {
            // 恢复原始样式
            this.restoreOverlayStyle(this.selectedOverlay, this.selectedType);

            // 移除高亮圆圈
            if (this.highlightCircle) {
                this.map.remove(this.highlightCircle);
                this.highlightCircle = null;
            }
        }

        this.selectedOverlay = null;
        this.selectedType = null;

        // 停止编辑
        this.stopEditing();

        // 更新UI
        this.updateSelectedInfo();
        this.updateUIState();
    }

    /**
     * 恢复覆盖物原始样式
     */
    restoreOverlayStyle(overlay, type) {
        const originalStyle = {
            strokeColor: this.styleSettings.strokeColor,
            strokeWeight: this.styleSettings.strokeWidth,
            fillColor: this.styleSettings.fillColor,
            fillOpacity: this.styleSettings.fillOpacity
        };

        switch (type) {
            case 'polyline':
                overlay.setOptions({
                    strokeColor: originalStyle.strokeColor,
                    strokeWeight: originalStyle.strokeWeight
                });
                break;

            case 'polygon':
            case 'circle':
            case 'rectangle':
                overlay.setOptions(originalStyle);
                break;
        }
    }

    /**
     * 更新选中对象信息
     */
    updateSelectedInfo() {
        const detailsElement = document.getElementById('selected-details');

        if (!this.selectedOverlay || !this.selectedType) {
            detailsElement.innerHTML = '<p class="no-selection">未选中任何对象</p>';
            return;
        }

        let info = `<div class="selected-object-info">`;
        info += `<p><strong>类型:</strong> ${this.getTypeDisplayName(this.selectedType)}</p>`;

        switch (this.selectedType) {
            case 'marker':
                const pos = this.selectedOverlay.getPosition();
                info += `<p><strong>坐标:</strong> ${pos.getLng().toFixed(6)}, ${pos.getLat().toFixed(6)}</p>`;
                break;

            case 'polyline':
                const path = this.selectedOverlay.getPath();
                const length = this.calculatePolylineLength(path);
                info += `<p><strong>节点数:</strong> ${path.length}</p>`;
                info += `<p><strong>长度:</strong> ${length.toFixed(2)} 米</p>`;
                break;

            case 'polygon':
                const polygonPath = this.selectedOverlay.getPath();
                const area = this.calculatePolygonArea(polygonPath);
                info += `<p><strong>节点数:</strong> ${polygonPath.length}</p>`;
                info += `<p><strong>面积:</strong> ${area.toFixed(2)} 平方米</p>`;
                break;

            case 'circle':
                const center = this.selectedOverlay.getCenter();
                const radius = this.selectedOverlay.getRadius();
                const circleArea = Math.PI * radius * radius;
                info += `<p><strong>中心:</strong> ${center.getLng().toFixed(6)}, ${center.getLat().toFixed(6)}</p>`;
                info += `<p><strong>半径:</strong> ${radius.toFixed(2)} 米</p>`;
                info += `<p><strong>面积:</strong> ${circleArea.toFixed(2)} 平方米</p>`;
                break;

            case 'rectangle':
                const bounds = this.selectedOverlay.getBounds();
                const rectArea = this.calculateRectangleArea(bounds);
                info += `<p><strong>范围:</strong> ${bounds.toString()}</p>`;
                info += `<p><strong>面积:</strong> ${rectArea.toFixed(2)} 平方米</p>`;
                break;
        }

        info += '</div>';
        detailsElement.innerHTML = info;
    }

    /**
     * 获取类型显示名称
     */
    getTypeDisplayName(type) {
        const names = {
            marker: '标记点',
            polyline: '线条',
            polygon: '多边形',
            circle: '圆形',
            rectangle: '矩形'
        };
        return names[type] || type;
    }

    /**
     * 计算线条长度
     */
    calculatePolylineLength(path) {
        let length = 0;
        for (let i = 1; i < path.length; i++) {
            length += AMap.GeometryUtil.distance(path[i - 1], path[i]);
        }
        return length;
    }

    /**
     * 计算多边形面积
     */
    calculatePolygonArea(path) {
        return AMap.GeometryUtil.ringArea(path);
    }

    /**
     * 计算矩形面积
     */
    calculateRectangleArea(bounds) {
        const sw = bounds.getSouthWest();
        const ne = bounds.getNorthEast();
        const width = AMap.GeometryUtil.distance(sw, [ne.getLng(), sw.getLat()]);
        const height = AMap.GeometryUtil.distance(sw, [sw.getLng(), ne.getLat()]);
        return width * height;
    }

    /**
     * 删除选中对象
     */
    deleteSelected() {
        if (!this.selectedOverlay || !this.selectedType) return;

        // 从地图移除
        this.map.remove(this.selectedOverlay);

        // 从集合中移除
        const collection = this.overlays[this.selectedType + 's'];
        const index = collection.indexOf(this.selectedOverlay);
        if (index > -1) {
            collection.splice(index, 1);
        }

        // 记录操作历史
        this.addToHistory('delete', this.selectedType, this.selectedOverlay);

        // 清除选择
        this.clearSelection();

        // 更新统计
        this.updateCounts();
    }

    /**
     * 复制选中对象
     */
    copySelected() {
        if (!this.selectedOverlay || !this.selectedType) return;

        this.copiedOverlay = this.selectedOverlay;
        this.copiedType = this.selectedType;

        // 更新UI状态
        this.updateUIState();

        this.showMessage('对象已复制');
    }

    /**
     * 粘贴对象
     */
    pasteObject() {
        if (!this.copiedOverlay || !this.copiedType) return;

        let newOverlay;
        const offset = 0.001; // 偏移量

        switch (this.copiedType) {
            case 'marker':
                const pos = this.copiedOverlay.getPosition();
                newOverlay = new AMap.Marker({
                    position: [pos.getLng() + offset, pos.getLat() + offset],
                    icon: this.copiedOverlay.getIcon()
                });
                break;

            case 'polyline':
                const path = this.copiedOverlay.getPath().map(point =>
                    [point.getLng() + offset, point.getLat() + offset]
                );
                newOverlay = new AMap.Polyline({
                    path: path,
                    strokeColor: this.copiedOverlay.getOptions().strokeColor,
                    strokeWeight: this.copiedOverlay.getOptions().strokeWeight
                });
                break;

            case 'polygon':
                const polygonPath = this.copiedOverlay.getPath().map(point =>
                    [point.getLng() + offset, point.getLat() + offset]
                );
                newOverlay = new AMap.Polygon({
                    path: polygonPath,
                    strokeColor: this.copiedOverlay.getOptions().strokeColor,
                    strokeWeight: this.copiedOverlay.getOptions().strokeWeight,
                    fillColor: this.copiedOverlay.getOptions().fillColor,
                    fillOpacity: this.copiedOverlay.getOptions().fillOpacity
                });
                break;

            case 'circle':
                const center = this.copiedOverlay.getCenter();
                newOverlay = new AMap.Circle({
                    center: [center.getLng() + offset, center.getLat() + offset],
                    radius: this.copiedOverlay.getRadius(),
                    strokeColor: this.copiedOverlay.getOptions().strokeColor,
                    strokeWeight: this.copiedOverlay.getOptions().strokeWeight,
                    fillColor: this.copiedOverlay.getOptions().fillColor,
                    fillOpacity: this.copiedOverlay.getOptions().fillOpacity
                });
                break;

            case 'rectangle':
                const bounds = this.copiedOverlay.getBounds();
                const sw = bounds.getSouthWest();
                const ne = bounds.getNorthEast();
                newOverlay = new AMap.Rectangle({
                    bounds: new AMap.Bounds(
                        [sw.getLng() + offset, sw.getLat() + offset],
                        [ne.getLng() + offset, ne.getLat() + offset]
                    ),
                    strokeColor: this.copiedOverlay.getOptions().strokeColor,
                    strokeWeight: this.copiedOverlay.getOptions().strokeWeight,
                    fillColor: this.copiedOverlay.getOptions().fillColor,
                    fillOpacity: this.copiedOverlay.getOptions().fillOpacity
                });
                break;
        }

        if (newOverlay) {
            // 添加到地图和集合
            this.map.add(newOverlay);
            this.overlays[this.copiedType + 's'].push(newOverlay);

            // 添加点击事件
            newOverlay.on('click', () => {
                this.selectOverlay(newOverlay, this.copiedType);
            });

            // 记录操作历史
            this.addToHistory('add', this.copiedType, newOverlay);

            // 选中新对象
            this.selectOverlay(newOverlay, this.copiedType);

            // 更新统计
            this.updateCounts();

            this.showMessage('对象已粘贴');
        }
    }

    /**
     * 编辑选中对象
     */
    editSelected() {
        if (!this.selectedOverlay || !this.selectedType) return;

        this.stopEditing();

        switch (this.selectedType) {
            case 'polyline':
            case 'polygon':
                this.polyEditor.setTarget(this.selectedOverlay);
                this.polyEditor.open();
                break;

            case 'circle':
                this.circleEditor.setTarget(this.selectedOverlay);
                this.circleEditor.open();
                break;

            case 'rectangle':
                this.rectangleEditor.setTarget(this.selectedOverlay);
                this.rectangleEditor.open();
                break;
        }

        this.showMessage('进入编辑模式，拖动节点进行编辑');
    }

    /**
     * 停止编辑
     */
    stopEditing() {
        this.polyEditor.close();
        this.circleEditor.close();
        this.rectangleEditor.close();
    }

    /**
     * 更新选中对象样式
     */
    updateSelectedStyle() {
        if (!this.selectedOverlay || !this.selectedType) return;

        const style = {
            strokeColor: this.styleSettings.strokeColor,
            strokeWeight: this.styleSettings.strokeWidth,
            fillColor: this.styleSettings.fillColor,
            fillOpacity: this.styleSettings.fillOpacity
        };

        switch (this.selectedType) {
            case 'polyline':
                this.selectedOverlay.setOptions({
                    strokeColor: style.strokeColor,
                    strokeWeight: style.strokeWeight
                });
                break;

            case 'polygon':
            case 'circle':
            case 'rectangle':
                this.selectedOverlay.setOptions(style);
                break;
        }

        // 重新高亮
        this.highlightOverlay(this.selectedOverlay, this.selectedType);
    }

    /**
     * 清空所有对象
     */
    clearAll() {
        // 移除所有覆盖物
        Object.values(this.overlays).forEach(collection => {
            collection.forEach(overlay => {
                this.map.remove(overlay);
            });
            collection.length = 0;
        });

        // 清除选择
        this.clearSelection();

        // 清除绘制状态
        this.clearDrawingState();

        // 清除历史
        this.history.actions = [];
        this.history.currentIndex = -1;

        // 更新统计和UI
        this.updateCounts();
        this.updateUIState();

        this.showMessage('已清空所有对象');
    }

    /**
     * 更新统计信息
     */
    updateCounts() {
        document.getElementById('marker-count').textContent = this.overlays.markers.length;
        document.getElementById('line-count').textContent = this.overlays.polylines.length;
        document.getElementById('polygon-count').textContent =
            this.overlays.polygons.length + this.overlays.circles.length + this.overlays.rectangles.length;
    }

    /**
     * 更新UI状态
     */
    updateUIState() {
        const hasSelection = this.selectedOverlay && this.selectedType;
        const hasCopied = this.copiedOverlay && this.copiedType;
        const hasHistory = this.history.actions.length > 0;

        document.getElementById('delete-selected').disabled = !hasSelection;
        document.getElementById('copy-selected').disabled = !hasSelection;
        document.getElementById('edit-selected').disabled = !hasSelection || this.selectedType === 'marker';
        document.getElementById('paste-object').disabled = !hasCopied;
        document.getElementById('undo-action').disabled = this.history.currentIndex < 0;
        document.getElementById('redo-action').disabled = this.history.currentIndex >= this.history.actions.length - 1;
    }

    /**
     * 添加到操作历史
     */
    addToHistory(action, type, overlay) {
        // 移除当前位置之后的历史
        this.history.actions = this.history.actions.slice(0, this.history.currentIndex + 1);

        // 添加新操作
        this.history.actions.push({
            action: action,
            type: type,
            overlay: overlay,
            timestamp: Date.now()
        });

        // 限制历史大小
        if (this.history.actions.length > this.history.maxSize) {
            this.history.actions.shift();
        } else {
            this.history.currentIndex++;
        }

        this.updateUIState();
    }

    /**
     * 撤销操作
     */
    undo() {
        if (this.history.currentIndex < 0) return;

        const action = this.history.actions[this.history.currentIndex];

        if (action.action === 'add') {
            // 撤销添加 = 删除
            this.map.remove(action.overlay);
            const collection = this.overlays[action.type + 's'];
            const index = collection.indexOf(action.overlay);
            if (index > -1) {
                collection.splice(index, 1);
            }
        } else if (action.action === 'delete') {
            // 撤销删除 = 添加回来
            this.map.add(action.overlay);
            this.overlays[action.type + 's'].push(action.overlay);
        }

        this.history.currentIndex--;
        this.updateCounts();
        this.updateUIState();
        this.showMessage('已撤销操作');
    }

    /**
     * 重做操作
     */
    redo() {
        if (this.history.currentIndex >= this.history.actions.length - 1) return;

        this.history.currentIndex++;
        const action = this.history.actions[this.history.currentIndex];

        if (action.action === 'add') {
            // 重做添加
            this.map.add(action.overlay);
            this.overlays[action.type + 's'].push(action.overlay);
        } else if (action.action === 'delete') {
            // 重做删除
            this.map.remove(action.overlay);
            const collection = this.overlays[action.type + 's'];
            const index = collection.indexOf(action.overlay);
            if (index > -1) {
                collection.splice(index, 1);
            }
        }

        this.updateCounts();
        this.updateUIState();
        this.showMessage('已重做操作');
    }

    /**
     * 导出数据
     */
    exportData() {
        const data = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            overlays: {}
        };

        // 导出各类覆盖物
        Object.keys(this.overlays).forEach(type => {
            data.overlays[type] = this.overlays[type].map(overlay => {
                return this.serializeOverlay(overlay, type.slice(0, -1));
            });
        });

        // 创建下载
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `amap-draw-${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showMessage('数据已导出');
    }

    /**
     * 序列化覆盖物
     */
    serializeOverlay(overlay, type) {
        const data = {
            type: type,
            options: overlay.getOptions()
        };

        switch (type) {
            case 'marker':
                data.position = overlay.getPosition();
                break;
            case 'polyline':
            case 'polygon':
                data.path = overlay.getPath();
                break;
            case 'circle':
                data.center = overlay.getCenter();
                data.radius = overlay.getRadius();
                break;
            case 'rectangle':
                data.bounds = overlay.getBounds();
                break;
        }

        return data;
    }

    /**
     * 导入数据
     */
    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                this.loadOverlaysFromData(data);
                this.showMessage('数据导入成功');
            } catch (error) {
                this.showMessage('导入失败：文件格式不正确', 'error');
            }
        };
        reader.readAsText(file);
    }

    /**
     * 从数据加载覆盖物
     */
    loadOverlaysFromData(data) {
        if (!data.overlays) return;

        Object.keys(data.overlays).forEach(type => {
            data.overlays[type].forEach(overlayData => {
                const overlay = this.createOverlayFromData(overlayData);
                if (overlay) {
                    this.map.add(overlay);
                    this.overlays[type].push(overlay);

                    // 添加点击事件
                    overlay.on('click', () => {
                        this.selectOverlay(overlay, overlayData.type);
                    });
                }
            });
        });

        this.updateCounts();
    }

    /**
     * 从数据创建覆盖物
     */
    createOverlayFromData(data) {
        switch (data.type) {
            case 'marker':
                return new AMap.Marker({
                    position: data.position,
                    ...data.options
                });
            case 'polyline':
                return new AMap.Polyline({
                    path: data.path,
                    ...data.options
                });
            case 'polygon':
                return new AMap.Polygon({
                    path: data.path,
                    ...data.options
                });
            case 'circle':
                return new AMap.Circle({
                    center: data.center,
                    radius: data.radius,
                    ...data.options
                });
            case 'rectangle':
                return new AMap.Rectangle({
                    bounds: data.bounds,
                    ...data.options
                });
        }
        return null;
    }

    /**
     * 显示绘制提示
     */
    showDrawTip(message) {
        const tip = document.getElementById('draw-tip');
        const text = document.getElementById('tip-text');
        text.textContent = message;
        tip.classList.remove('hidden');
    }

    /**
     * 隐藏绘制提示
     */
    hideDrawTip() {
        document.getElementById('draw-tip').classList.add('hidden');
    }

    /**
     * 显示确认对话框
     */
    showConfirmDialog(title, message, callback) {
        document.getElementById('confirm-title').textContent = title;
        document.getElementById('confirm-message').textContent = message;
        document.getElementById('confirm-dialog').classList.remove('hidden');
        this.confirmCallback = callback;
    }

    /**
     * 隐藏确认对话框
     */
    hideConfirmDialog() {
        document.getElementById('confirm-dialog').classList.add('hidden');
        this.confirmCallback = null;
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 记录到控制台
        console.log(`[${type.toUpperCase()}] ${message}`);

        // 创建临时消息框
        const messageBox = document.createElement('div');
        messageBox.className = `message-box message-${type}`;
        messageBox.textContent = message;

        document.body.appendChild(messageBox);

        // 添加样式
        messageBox.style.position = 'fixed';
        messageBox.style.top = '20px';
        messageBox.style.left = '50%';
        messageBox.style.transform = 'translateX(-50%)';
        messageBox.style.padding = '10px 20px';
        messageBox.style.borderRadius = '4px';
        messageBox.style.zIndex = '10000';
        messageBox.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
        messageBox.style.fontSize = '14px';

        if (type === 'error') {
            messageBox.style.backgroundColor = '#f44336';
            messageBox.style.color = 'white';
        } else if (type === 'success') {
            messageBox.style.backgroundColor = '#4caf50';
            messageBox.style.color = 'white';
        } else {
            messageBox.style.backgroundColor = 'rgba(0,0,0,0.8)';
            messageBox.style.color = 'white';
        }

        // 自动消失
        setTimeout(() => {
            messageBox.style.opacity = '0';
            messageBox.style.transition = 'opacity 0.5s';
            setTimeout(() => {
                document.body.removeChild(messageBox);
            }, 500);
        }, 3000);
    }
}

// 等待高德地图API加载完成后初始化
function initializeApp() {
    console.log('开始初始化应用...');

    // 检查是否有高德地图API
    if (typeof AMap === 'undefined') {
        console.error('高德地图API未加载');
        alert('请先配置高德地图API Key！\n请在HTML文件中将YOUR_API_KEY替换为你的实际API Key。');
        return;
    }

    console.log('高德地图API已加载，开始初始化地图工具');

    // 创建地图绘制工具实例
    try {
        window.amapDrawTool = new AMapDrawingTool();
        console.log('地图工具实例创建成功');
    } catch (error) {
        console.error('创建地图工具实例失败:', error);
        alert('初始化失败: ' + error.message);
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM已经加载完成
    initializeApp();
}
