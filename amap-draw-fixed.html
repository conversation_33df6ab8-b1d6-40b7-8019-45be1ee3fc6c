<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具 - 修复版</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #f5f5f5;
            padding: 20px;
            border-right: 1px solid #ddd;
        }
        
        .tool-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .tool-btn.active {
            background: #007bff;
            color: white;
        }
        
        .tool-btn:hover {
            background: #e9ecef;
        }
        
        .tool-btn.active:hover {
            background: #0056b3;
        }
        
        #map {
            flex: 1;
            height: 100%;
        }
        
        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .tip {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
            display: none;
        }
        
        .shortcut-tip {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .shortcut-tip kbd {
            background: #555;
            border: 1px solid #777;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: bold;
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>绘制工具</h3>
            
            <button class="tool-btn active" data-tool="marker">📍 标记点</button>
            <button class="tool-btn" data-tool="polyline">📏 绘制线</button>
            <button class="tool-btn" data-tool="polygon">🔷 绘制面</button>
            <button class="tool-btn" data-tool="rectangle">⬜ 矩形</button>
            <button class="tool-btn" data-tool="circle">⭕ 圆形</button>
            <button class="tool-btn" data-tool="select">👆 选择</button>
            
            <hr style="margin: 20px 0;">
            
            <button id="clear-all" style="width: 100%; padding: 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">清空所有</button>
            
            <div style="margin-top: 20px; font-size: 12px; color: #666;">
                <strong>使用说明:</strong><br>
                • 线条/多边形: 双击结束绘制<br>
                • 矩形/圆形: 拖动绘制<br>
                • 按 ESC 键取消当前绘制<br>
            </div>
        </div>
        
        <div style="flex: 1; position: relative;">
            <div id="map"></div>
            <div class="status">
                <div>当前工具: <span id="current-tool">标记点</span></div>
                <div>状态: <span id="status">就绪</span></div>
            </div>
            <div id="tip" class="tip">点击地图开始绘制</div>
            <div class="shortcut-tip">
                按 <kbd>ESC</kbd> 取消绘制
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化地图
            const map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923],
                viewMode: '2D'
            });
            
            // 初始化鼠标工具
            const mouseTool = new AMap.MouseTool(map);
            
            // 当前工具
            let currentTool = 'marker';
            
            // 覆盖物存储
            const overlays = [];
            
            // 显示提示
            function showTip(message) {
                const tip = document.getElementById('tip');
                tip.textContent = message;
                tip.style.display = 'block';
            }
            
            // 隐藏提示
            function hideTip() {
                document.getElementById('tip').style.display = 'none';
            }
            
            // 取消当前绘制
            function cancelDraw() {
                mouseTool.close();
                hideTip();
                setTool('select');
                document.getElementById('status').textContent = '已取消绘制';
            }
            
            // 设置工具函数
            function setTool(toolName) {
                currentTool = toolName;
                document.getElementById('current-tool').textContent = toolName;
                document.getElementById('status').textContent = '工具已切换';
                
                // 更新按钮状态
                document.querySelectorAll('.tool-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.tool === toolName);
                });
                
                // 关闭当前工具
                mouseTool.close();
                
                // 启动新工具
                switch(toolName) {
                    case 'marker':
                        mouseTool.marker();
                        showTip('点击地图添加标记点，按ESC取消');
                        break;
                    case 'polyline':
                        mouseTool.polyline();
                        showTip('点击地图开始绘制线条，双击结束，按ESC取消');
                        break;
                    case 'polygon':
                        mouseTool.polygon();
                        showTip('点击地图开始绘制多边形，双击结束，按ESC取消');
                        break;
                    case 'rectangle':
                        mouseTool.rectangle();
                        showTip('点击并拖动绘制矩形，按ESC取消');
                        break;
                    case 'circle':
                        mouseTool.circle();
                        showTip('点击并拖动绘制圆形，按ESC取消');
                        break;
                    case 'select':
                        hideTip();
                        break;
                }
            }
            
            // 绑定按钮事件
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const tool = this.dataset.tool;
                    setTool(tool);
                });
            });
            
            // 清空按钮
            document.getElementById('clear-all').addEventListener('click', function() {
                overlays.forEach(overlay => {
                    map.remove(overlay);
                });
                overlays.length = 0;
                document.getElementById('status').textContent = '已清空';
            });
            
            // 键盘事件 - ESC取消绘制
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    cancelDraw();
                }
            });
            
            // 监听绘制完成
            mouseTool.on('draw', function(e) {
                overlays.push(e.obj);
                document.getElementById('status').textContent = `绘制了 ${e.type}`;
                
                // 对于标记点，继续使用当前工具
                // 对于其他图形，切换到选择模式
                if (e.type !== 'marker') {
                    setTimeout(() => {
                        setTool('select');
                    }, 100);
                }
            });
            
            // 设置默认工具
            setTool('marker');
        });
    </script>
</body>
</html>
