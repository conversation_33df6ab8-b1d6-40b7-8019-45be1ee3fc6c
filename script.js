class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.scoreElement = document.getElementById('score');
        this.highScoreElement = document.getElementById('high-score');
        this.gameOverElement = document.getElementById('game-over');
        this.finalScoreElement = document.getElementById('final-score');
        this.restartBtn = document.getElementById('restart-btn');
        
        // 游戏设置
        this.gridSize = 20;
        this.tileCount = this.canvas.width / this.gridSize;
        
        // 游戏状态
        this.snake = [{ x: 10, y: 10 }];
        this.food = {};
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.highScore = localStorage.getItem('snakeHighScore') || 0;
        this.gameRunning = false;
        this.gamePaused = false;
        
        this.init();
    }
    
    init() {
        this.highScoreElement.textContent = this.highScore;
        this.generateFood();
        this.setupEventListeners();
        this.gameLoop();
    }
    
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning && e.code !== 'Space') return;
            
            switch(e.code) {
                case 'ArrowUp':
                    if (this.dy !== 1) { this.dx = 0; this.dy = -1; }
                    break;
                case 'ArrowDown':
                    if (this.dy !== -1) { this.dx = 0; this.dy = 1; }
                    break;
                case 'ArrowLeft':
                    if (this.dx !== 1) { this.dx = -1; this.dy = 0; }
                    break;
                case 'ArrowRight':
                    if (this.dx !== -1) { this.dx = 1; this.dy = 0; }
                    break;
                case 'Space':
                    this.togglePause();
                    break;
            }
            
            if (!this.gameRunning && (this.dx !== 0 || this.dy !== 0)) {
                this.startGame();
            }
        });
        
        // 按钮控制
        document.getElementById('up-btn').addEventListener('click', () => {
            if (this.dy !== 1) { this.dx = 0; this.dy = -1; }
            if (!this.gameRunning) this.startGame();
        });
        
        document.getElementById('down-btn').addEventListener('click', () => {
            if (this.dy !== -1) { this.dx = 0; this.dy = 1; }
            if (!this.gameRunning) this.startGame();
        });
        
        document.getElementById('left-btn').addEventListener('click', () => {
            if (this.dx !== 1) { this.dx = -1; this.dy = 0; }
            if (!this.gameRunning) this.startGame();
        });
        
        document.getElementById('right-btn').addEventListener('click', () => {
            if (this.dx !== -1) { this.dx = 1; this.dy = 0; }
            if (!this.gameRunning) this.startGame();
        });
        
        document.getElementById('pause-btn').addEventListener('click', () => {
            this.togglePause();
        });
        
        this.restartBtn.addEventListener('click', () => {
            this.restart();
        });
    }
    
    startGame() {
        this.gameRunning = true;
        this.gamePaused = false;
    }
    
    togglePause() {
        if (this.gameRunning) {
            this.gamePaused = !this.gamePaused;
        }
    }
    
    generateFood() {
        this.food = {
            x: Math.floor(Math.random() * this.tileCount),
            y: Math.floor(Math.random() * this.tileCount)
        };
        
        // 确保食物不在蛇身上
        for (let segment of this.snake) {
            if (segment.x === this.food.x && segment.y === this.food.y) {
                this.generateFood();
                return;
            }
        }
    }
    
    update() {
        if (!this.gameRunning || this.gamePaused) return;
        
        const head = { x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy };
        
        // 检查墙壁碰撞
        if (head.x < 0 || head.x >= this.tileCount || head.y < 0 || head.y >= this.tileCount) {
            this.gameOver();
            return;
        }
        
        // 检查自身碰撞
        for (let segment of this.snake) {
            if (head.x === segment.x && head.y === segment.y) {
                this.gameOver();
                return;
            }
        }
        
        this.snake.unshift(head);
        
        // 检查是否吃到食物
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.scoreElement.textContent = this.score;
            this.generateFood();
        } else {
            this.snake.pop();
        }
    }
    
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#e8f5e9';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制蛇
        this.ctx.fillStyle = '#4caf50';
        for (let segment of this.snake) {
            this.ctx.fillRect(segment.x * this.gridSize, segment.y * this.gridSize, this.gridSize - 2, this.gridSize - 2);
        }
        
        // 绘制蛇头（不同颜色）
        this.ctx.fillStyle = '#2e7d32';
        this.ctx.fillRect(this.snake[0].x * this.gridSize, this.snake[0].y * this.gridSize, this.gridSize - 2, this.gridSize - 2);
        
        // 绘制食物
        this.ctx.fillStyle = '#f44336';
        this.ctx.fillRect(this.food.x * this.gridSize, this.food.y * this.gridSize, this.gridSize - 2, this.gridSize - 2);
    }
    
    gameOver() {
        this.gameRunning = false;
        
        if (this.score > this.highScore) {
            this.highScore = this.score;
            this.highScoreElement.textContent = this.highScore;
            localStorage.setItem('snakeHighScore', this.highScore);
        }
        
        this.finalScoreElement.textContent = this.score;
        this.gameOverElement.classList.remove('hidden');
    }
    
    restart() {
        this.snake = [{ x: 10, y: 10 }];
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.scoreElement.textContent = this.score;
        this.gameRunning = false;
        this.gamePaused = false;
        this.generateFood();
        this.gameOverElement.classList.add('hidden');
    }
    
    gameLoop() {
        this.update();
        this.draw();
        setTimeout(() => this.gameLoop(), 150);
    }
}

// 启动游戏
window.addEventListener('load', () => {
    new SnakeGame();
});
