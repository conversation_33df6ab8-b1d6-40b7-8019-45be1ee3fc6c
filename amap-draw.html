<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制工具 - 支持道路吸附</title>
    
    <!-- 高德地图API - 请替换为你的API Key -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor,AMap.CircleEditor,AMap.RectangleEditor,AMap.Driving,AMap.Geocoder,AMap.PlaceSearch"></script>
    
    <link rel="stylesheet" href="amap-draw-style.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="header">
            <div class="header-left">
                <h1>🗺️ 高德地图绘制工具</h1>
                <div class="snap-toggle">
                    <button id="toggle-snap" class="btn btn-success active" data-snap="true">
                        🧲 道路吸附: 开
                    </button>
                </div>
            </div>
            <div class="header-right">
                <button id="undo-action" class="btn btn-secondary" disabled title="撤销">↶</button>
                <button id="redo-action" class="btn btn-secondary" disabled title="重做">↷</button>
                <button id="clear-all" class="btn btn-danger" title="清空所有">🗑️ 清空</button>
                <button id="export-data" class="btn btn-primary" title="导出数据">📤 导出</button>
                <input type="file" id="import-data" accept=".json" style="display: none;">
                <button id="import-btn" class="btn btn-secondary" title="导入数据">📥 导入</button>
            </div>
        </header>

        <div class="main-content">
            <!-- 左侧工具面板 -->
            <div class="sidebar">
                <!-- 绘制工具区域 -->
                <div class="tool-section">
                    <h3>🎨 绘制工具</h3>
                    <div class="tool-buttons">
                        <button id="draw-marker" class="tool-btn active" data-tool="marker" title="绘制标记点">
                            <span class="tool-icon">📍</span>
                            <span class="tool-text">标记点</span>
                        </button>
                        <button id="draw-polyline" class="tool-btn" data-tool="polyline" title="绘制线条">
                            <span class="tool-icon">📏</span>
                            <span class="tool-text">绘制线</span>
                        </button>
                        <button id="draw-polygon" class="tool-btn" data-tool="polygon" title="绘制多边形">
                            <span class="tool-icon">🔷</span>
                            <span class="tool-text">绘制面</span>
                        </button>
                        <button id="draw-rectangle" class="tool-btn" data-tool="rectangle" title="绘制矩形">
                            <span class="tool-icon">⬜</span>
                            <span class="tool-text">矩形</span>
                        </button>
                        <button id="draw-circle" class="tool-btn" data-tool="circle" title="绘制圆形">
                            <span class="tool-icon">⭕</span>
                            <span class="tool-text">圆形</span>
                        </button>
                        <button id="select-mode" class="tool-btn" data-tool="select" title="选择和编辑">
                            <span class="tool-icon">👆</span>
                            <span class="tool-text">选择</span>
                        </button>
                    </div>
                </div>

                <!-- 道路吸附设置 -->
                <div class="snap-section">
                    <h3>🧲 道路吸附</h3>
                    <div class="snap-controls">
                        <div class="control-group">
                            <label for="snap-distance">吸附距离:</label>
                            <div class="range-control">
                                <input type="range" id="snap-distance" min="10" max="200" value="50">
                                <span id="distance-value">50m</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="snap-to-roads" checked>
                                <span class="checkmark"></span>
                                吸附到道路
                            </label>
                        </div>
                        <div class="control-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto-route" checked>
                                <span class="checkmark"></span>
                                自动路径规划
                            </label>
                        </div>
                    </div>
                </div>

                <!-- 样式设置 -->
                <div class="style-section">
                    <h3>🎨 样式设置</h3>
                    <div class="style-controls">
                        <div class="control-group">
                            <label for="stroke-color">线条颜色:</label>
                            <input type="color" id="stroke-color" value="#ff4444">
                        </div>
                        <div class="control-group">
                            <label for="fill-color">填充颜色:</label>
                            <input type="color" id="fill-color" value="#ff4444">
                        </div>
                        <div class="control-group">
                            <label for="stroke-width">线条粗细:</label>
                            <div class="range-control">
                                <input type="range" id="stroke-width" min="1" max="10" value="3">
                                <span id="width-value">3px</span>
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="fill-opacity">填充透明度:</label>
                            <div class="range-control">
                                <input type="range" id="fill-opacity" min="0" max="1" step="0.1" value="0.3">
                                <span id="opacity-value">0.3</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图层信息 -->
                <div class="info-section">
                    <h3>📊 图层信息</h3>
                    <div class="info-stats">
                        <div class="stat-item">
                            <span class="stat-icon">📍</span>
                            <span class="stat-label">标记点:</span>
                            <span id="marker-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-icon">📏</span>
                            <span class="stat-label">线条:</span>
                            <span id="line-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-icon">🔷</span>
                            <span class="stat-label">多边形:</span>
                            <span id="polygon-count" class="stat-value">0</span>
                        </div>
                    </div>
                    
                    <div class="selected-info">
                        <h4>选中对象信息</h4>
                        <div id="selected-details" class="details-content">
                            <p class="no-selection">未选中任何对象</p>
                        </div>
                    </div>
                </div>

                <!-- 编辑操作 -->
                <div class="edit-section">
                    <h3>✏️ 编辑操作</h3>
                    <div class="edit-controls">
                        <button id="delete-selected" class="btn btn-danger btn-small" disabled>删除选中</button>
                        <button id="copy-selected" class="btn btn-secondary btn-small" disabled>复制</button>
                        <button id="paste-object" class="btn btn-secondary btn-small" disabled>粘贴</button>
                        <button id="edit-selected" class="btn btn-primary btn-small" disabled>编辑节点</button>
                    </div>
                </div>
            </div>

            <!-- 地图容器 -->
            <div class="map-container">
                <div id="map"></div>
                
                <!-- 地图状态栏 -->
                <div class="map-status">
                    <div class="status-item">
                        <span class="status-label">坐标:</span>
                        <span id="mouse-coordinates" class="status-value">--, --</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">缩放:</span>
                        <span id="zoom-level" class="status-value">--</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">工具:</span>
                        <span id="current-tool-name" class="status-value">标记点</span>
                    </div>
                </div>
                
                <!-- 道路吸附指示器 -->
                <div id="snap-indicator" class="snap-indicator hidden">
                    <div class="snap-point"></div>
                    <div class="snap-text">道路吸附</div>
                </div>
                
                <!-- 绘制提示 -->
                <div id="draw-tip" class="draw-tip hidden">
                    <div class="tip-content">
                        <span id="tip-text">点击地图开始绘制</span>
                    </div>
                </div>

                <!-- 快捷键提示 -->
                <div class="shortcut-tip">
                    <div class="shortcut-content">
                        <strong>快捷键:</strong> 按 <kbd>ESC</kbd> 取消当前绘制
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-dialog" class="modal hidden">
        <div class="modal-content">
            <h3 id="confirm-title">确认操作</h3>
            <p id="confirm-message">确定要执行此操作吗？</p>
            <div class="modal-buttons">
                <button id="confirm-yes" class="btn btn-danger">确定</button>
                <button id="confirm-no" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="amap-draw-script.js"></script>
</body>
</html>
