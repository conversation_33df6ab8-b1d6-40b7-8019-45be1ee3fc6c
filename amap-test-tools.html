<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图工具测试</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool,AMap.PolyEditor"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 200px;
            background: #f5f5f5;
            padding: 20px;
            border-right: 1px solid #ddd;
        }
        
        .tool-btn {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .tool-btn.active {
            background: #007bff;
            color: white;
        }
        
        .tool-btn:hover {
            background: #e9ecef;
        }
        
        .tool-btn.active:hover {
            background: #0056b3;
        }
        
        #map {
            flex: 1;
            height: 100%;
        }
        
        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .debug {
            margin-top: 20px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h3>绘制工具</h3>
            
            <button class="tool-btn active" data-tool="marker">📍 标记点</button>
            <button class="tool-btn" data-tool="polyline">📏 绘制线</button>
            <button class="tool-btn" data-tool="polygon">🔷 绘制面</button>
            <button class="tool-btn" data-tool="rectangle">⬜ 矩形</button>
            <button class="tool-btn" data-tool="circle">⭕ 圆形</button>
            <button class="tool-btn" data-tool="select">👆 选择</button>
            
            <hr style="margin: 20px 0;">
            
            <button id="clear-all" style="width: 100%; padding: 10px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">清空所有</button>
            
            <div class="debug" id="debug-log">
                <strong>调试信息:</strong><br>
            </div>
        </div>
        
        <div style="flex: 1; position: relative;">
            <div id="map"></div>
            <div class="status">
                <div>当前工具: <span id="current-tool">标记点</span></div>
                <div>状态: <span id="status">就绪</span></div>
            </div>
        </div>
    </div>
    
    <script>
        // 调试日志函数
        function log(message) {
            console.log(message);
            const debugLog = document.getElementById('debug-log');
            debugLog.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成');
            
            // 检查高德地图API
            if (typeof AMap === 'undefined') {
                log('错误: 高德地图API未加载');
                return;
            }
            
            log('高德地图API已加载');
            
            // 初始化地图
            const map = new AMap.Map('map', {
                zoom: 13,
                center: [116.397428, 39.90923],
                viewMode: '2D'
            });
            
            log('地图初始化完成');
            
            // 初始化鼠标工具
            const mouseTool = new AMap.MouseTool(map);
            log('鼠标工具初始化完成');
            
            // 当前工具
            let currentTool = 'marker';
            
            // 覆盖物存储
            const overlays = [];
            
            // 设置工具函数
            function setTool(toolName) {
                log(`切换到工具: ${toolName}`);
                
                currentTool = toolName;
                document.getElementById('current-tool').textContent = toolName;
                document.getElementById('status').textContent = '工具已切换';
                
                // 更新按钮状态
                document.querySelectorAll('.tool-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.tool === toolName);
                });
                
                // 关闭当前工具
                mouseTool.close();
                
                // 启动新工具
                try {
                    switch(toolName) {
                        case 'marker':
                            mouseTool.marker();
                            break;
                        case 'polyline':
                            mouseTool.polyline();
                            break;
                        case 'polygon':
                            mouseTool.polygon();
                            break;
                        case 'rectangle':
                            mouseTool.rectangle();
                            break;
                        case 'circle':
                            mouseTool.circle();
                            break;
                        case 'select':
                            // 选择模式不需要启动工具
                            break;
                    }
                    log(`${toolName} 工具启动成功`);
                } catch (error) {
                    log(`启动工具失败: ${error.message}`);
                }
            }
            
            // 绑定按钮事件
            document.querySelectorAll('.tool-btn').forEach(btn => {
                log(`绑定按钮事件: ${btn.dataset.tool}`);
                
                btn.addEventListener('click', function() {
                    const tool = this.dataset.tool;
                    log(`按钮点击: ${tool}`);
                    setTool(tool);
                });
            });
            
            // 清空按钮
            document.getElementById('clear-all').addEventListener('click', function() {
                log('清空所有覆盖物');
                overlays.forEach(overlay => {
                    map.remove(overlay);
                });
                overlays.length = 0;
                document.getElementById('status').textContent = '已清空';
            });
            
            // 监听绘制完成
            mouseTool.on('draw', function(e) {
                log(`绘制完成: ${e.type}`);
                overlays.push(e.obj);
                document.getElementById('status').textContent = `绘制了 ${e.type}`;
            });
            
            // 设置默认工具
            setTool('marker');
            
            log('应用初始化完成');
        });
    </script>
</body>
</html>
