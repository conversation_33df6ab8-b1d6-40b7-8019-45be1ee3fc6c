<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高德地图绘制测试</title>
    
    <!-- 高德地图API -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=3cadc8481b811275d53b1df97013ae16&plugin=AMap.MouseTool"></script>
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        #map {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button.active {
            background-color: #28a745;
        }
        
        .info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>高德地图绘制功能测试</h1>
    
    <div class="controls">
        <button id="btn-marker" class="active">绘制标记点</button>
        <button id="btn-polyline">绘制线条</button>
        <button id="btn-polygon">绘制多边形</button>
        <button id="btn-rectangle">绘制矩形</button>
        <button id="btn-circle">绘制圆形</button>
        <button id="btn-clear">清空所有</button>
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <div>当前工具: <span id="current-tool">标记点</span></div>
        <div>绘制数量: <span id="count">0</span></div>
        <div>状态: <span id="status">就绪</span></div>
    </div>
    
    <div id="error-info" class="error" style="display: none;"></div>

    <script>
        class SimpleMapTest {
            constructor() {
                this.map = null;
                this.mouseTool = null;
                this.currentTool = 'marker';
                this.overlays = [];
                
                this.init();
            }
            
            init() {
                console.log('开始初始化测试应用...');
                this.showStatus('初始化中...');
                
                try {
                    // 检查高德地图API
                    if (typeof AMap === 'undefined') {
                        throw new Error('高德地图API未加载');
                    }
                    
                    this.initMap();
                    this.initEventListeners();
                    this.showStatus('初始化完成');
                    
                } catch (error) {
                    this.showError('初始化失败: ' + error.message);
                    console.error('初始化失败:', error);
                }
            }
            
            initMap() {
                console.log('创建地图...');
                
                this.map = new AMap.Map('map', {
                    zoom: 13,
                    center: [116.397428, 39.90923],
                    viewMode: '2D'
                });
                
                console.log('地图创建完成:', this.map);
                
                this.map.on('complete', () => {
                    console.log('地图加载完成');
                    this.initMouseTool();
                });
            }
            
            initMouseTool() {
                console.log('初始化鼠标工具...');
                
                try {
                    this.mouseTool = new AMap.MouseTool(this.map);
                    console.log('鼠标工具创建完成:', this.mouseTool);
                    
                    // 绑定绘制完成事件
                    this.mouseTool.on('draw', (e) => {
                        console.log('绘制完成:', e);
                        this.onDrawComplete(e);
                    });
                    
                    // 设置默认工具
                    this.setTool('marker');
                    
                } catch (error) {
                    this.showError('鼠标工具初始化失败: ' + error.message);
                    console.error('鼠标工具初始化失败:', error);
                }
            }
            
            initEventListeners() {
                document.getElementById('btn-marker').addEventListener('click', () => this.setTool('marker'));
                document.getElementById('btn-polyline').addEventListener('click', () => this.setTool('polyline'));
                document.getElementById('btn-polygon').addEventListener('click', () => this.setTool('polygon'));
                document.getElementById('btn-rectangle').addEventListener('click', () => this.setTool('rectangle'));
                document.getElementById('btn-circle').addEventListener('click', () => this.setTool('circle'));
                document.getElementById('btn-clear').addEventListener('click', () => this.clearAll());
            }
            
            setTool(tool) {
                console.log('设置工具:', tool);
                this.currentTool = tool;
                
                // 更新按钮状态
                document.querySelectorAll('button').forEach(btn => btn.classList.remove('active'));
                document.getElementById('btn-' + tool).classList.add('active');
                document.getElementById('current-tool').textContent = this.getToolName(tool);
                
                try {
                    // 关闭当前工具
                    this.mouseTool.close();
                    
                    // 设置新工具
                    switch (tool) {
                        case 'marker':
                            this.mouseTool.marker();
                            this.showStatus('点击地图添加标记点');
                            break;
                        case 'polyline':
                            this.mouseTool.polyline({
                                strokeColor: '#ff0000',
                                strokeWeight: 3
                            });
                            this.showStatus('点击地图开始绘制线条，双击结束');
                            break;
                        case 'polygon':
                            this.mouseTool.polygon({
                                strokeColor: '#ff0000',
                                strokeWeight: 3,
                                fillColor: '#ff0000',
                                fillOpacity: 0.3
                            });
                            this.showStatus('点击地图开始绘制多边形，双击结束');
                            break;
                        case 'rectangle':
                            this.mouseTool.rectangle({
                                strokeColor: '#ff0000',
                                strokeWeight: 3,
                                fillColor: '#ff0000',
                                fillOpacity: 0.3
                            });
                            this.showStatus('点击并拖动绘制矩形');
                            break;
                        case 'circle':
                            this.mouseTool.circle({
                                strokeColor: '#ff0000',
                                strokeWeight: 3,
                                fillColor: '#ff0000',
                                fillOpacity: 0.3
                            });
                            this.showStatus('点击并拖动绘制圆形');
                            break;
                    }
                } catch (error) {
                    this.showError('设置工具失败: ' + error.message);
                    console.error('设置工具失败:', error);
                }
            }
            
            onDrawComplete(e) {
                console.log('绘制完成回调:', e);
                
                const overlay = e.obj;
                this.overlays.push(overlay);
                
                this.updateCount();
                this.showStatus('绘制完成！对象已添加到地图');
                
                // 添加点击事件
                overlay.on('click', () => {
                    console.log('点击了绘制的对象:', overlay);
                });
            }
            
            clearAll() {
                console.log('清空所有对象');
                
                this.overlays.forEach(overlay => {
                    this.map.remove(overlay);
                });
                
                this.overlays = [];
                this.updateCount();
                this.showStatus('已清空所有对象');
            }
            
            updateCount() {
                document.getElementById('count').textContent = this.overlays.length;
            }
            
            getToolName(tool) {
                const names = {
                    marker: '标记点',
                    polyline: '线条',
                    polygon: '多边形',
                    rectangle: '矩形',
                    circle: '圆形'
                };
                return names[tool] || tool;
            }
            
            showStatus(message) {
                document.getElementById('status').textContent = message;
                console.log('状态:', message);
            }
            
            showError(message) {
                const errorDiv = document.getElementById('error-info');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                console.error('错误:', message);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            window.mapTest = new SimpleMapTest();
        });
    </script>
</body>
</html>
