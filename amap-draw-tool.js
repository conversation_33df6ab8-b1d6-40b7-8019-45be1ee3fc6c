/**
 * 高德地图绘制工具
 * 支持点、线、面的绘制，以及道路自动吸附功能
 */

class AMapDrawTool {
    constructor() {
        this.map = null;
        this.mouseTool = null;
        this.drivingService = null;
        
        // 当前工具
        this.currentTool = 'marker';
        
        // 绘制的图形集合
        this.overlays = {
            markers: [],
            polylines: [],
            polygons: [],
            circles: [],
            rectangles: []
        };
        
        // 道路吸附设置
        this.snapSettings = {
            enabled: true,
            distance: 50
        };
        
        // 绘制状态
        this.drawingState = {
            isDrawing: false,
            tempPoints: [],
            tempMarkers: [],
            tempPolyline: null
        };
        
        this.init();
    }
    
    init() {
        console.log('初始化地图绘制工具...');
        
        // 检查高德地图API
        if (typeof AMap === 'undefined') {
            console.error('高德地图API未加载');
            alert('高德地图API未加载，请检查网络连接');
            return;
        }
        
        this.initMap();
        this.initTools();
        this.initEventListeners();
        
        console.log('地图绘制工具初始化完成');
    }
    
    initMap() {
        this.map = new AMap.Map('map', {
            zoom: 13,
            center: [116.397428, 39.90923],
            viewMode: '2D',
            resizeEnable: true,
            rotateEnable: false,
            pitchEnable: false,
            mapStyle: 'amap://styles/normal'
        });
        
        // 添加地图控件
        this.map.addControl(new AMap.Scale());
        this.map.addControl(new AMap.ToolBar({
            position: 'RB'
        }));
        
        // 鼠标坐标显示
        this.map.on('mousemove', (e) => {
            const lng = e.lnglat.getLng().toFixed(6);
            const lat = e.lnglat.getLat().toFixed(6);
            document.getElementById('mouse-coordinates').textContent = `${lng}, ${lat}`;
        });
    }
    
    initTools() {
        // 初始化鼠标工具
        this.mouseTool = new AMap.MouseTool(this.map);
        
        // 初始化路径规划服务
        this.drivingService = new AMap.Driving({
            map: this.map,
            hideMarkers: true,
            isOutline: false,
            showTraffic: false,
            autoFitView: false
        });
        
        // 监听绘制完成事件
        this.mouseTool.on('draw', (e) => {
            this.handleDrawComplete(e);
        });
    }
    
    initEventListeners() {
        // 工具按钮点击事件
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tool = e.currentTarget.dataset.tool;
                this.setActiveTool(tool);
            });
        });
        
        // 道路吸附开关
        document.getElementById('road-snap').addEventListener('change', (e) => {
            this.snapSettings.enabled = e.target.checked;
            document.getElementById('snap-status').textContent = e.target.checked ? '已开启' : '已关闭';
            console.log('道路吸附:', e.target.checked ? '开启' : '关闭');
        });
        
        // 吸附距离调节
        document.getElementById('snap-distance').addEventListener('input', (e) => {
            this.snapSettings.distance = parseInt(e.target.value);
            document.getElementById('distance-value').textContent = `${e.target.value}m`;
        });
        
        // 清空所有按钮
        document.getElementById('clear-all').addEventListener('click', () => {
            this.clearAll();
        });
        
        // 键盘事件 - ESC取消绘制
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.cancelDraw();
            }
        });
    }
    
    setActiveTool(tool) {
        console.log('切换工具:', tool);
        
        this.currentTool = tool;
        
        // 更新UI
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tool === tool);
        });
        
        const toolNames = {
            marker: '标记点',
            polyline: '绘制线',
            polygon: '绘制面',
            rectangle: '矩形',
            circle: '圆形',
            select: '选择'
        };
        
        document.getElementById('current-tool').textContent = toolNames[tool] || tool;
        
        // 设置鼠标工具
        this.setMouseTool(tool);
    }
    
    setMouseTool(tool) {
        // 关闭当前工具
        this.mouseTool.close();
        this.clearDrawingState();
        
        switch (tool) {
            case 'marker':
                this.mouseTool.marker();
                this.showTip('点击地图添加标记点');
                break;
                
            case 'polyline':
                if (this.snapSettings.enabled) {
                    this.startCustomPolylineDraw();
                } else {
                    this.mouseTool.polyline();
                    this.showTip('点击地图开始绘制线条，双击结束');
                }
                break;
                
            case 'polygon':
                this.mouseTool.polygon();
                this.showTip('点击地图开始绘制多边形，双击结束');
                break;
                
            case 'rectangle':
                this.mouseTool.rectangle();
                this.showTip('点击并拖动绘制矩形');
                break;
                
            case 'circle':
                this.mouseTool.circle();
                this.showTip('点击并拖动绘制圆形');
                break;
                
            case 'select':
                this.hideTip();
                break;
        }
    }
    
    startCustomPolylineDraw() {
        this.drawingState.isDrawing = true;
        this.drawingState.tempPoints = [];
        this.drawingState.tempMarkers = [];
        this.drawingState.tempPolyline = null;
        
        this.showTip('点击地图开始绘制路径（支持道路吸附），双击结束');
        
        // 监听地图点击事件
        this.map.on('click', this.handlePolylineClick.bind(this));
        this.map.on('dblclick', this.handlePolylineDoubleClick.bind(this));
    }
    
    handlePolylineClick(e) {
        if (!this.drawingState.isDrawing || this.currentTool !== 'polyline') return;
        
        const point = [e.lnglat.getLng(), e.lnglat.getLat()];
        this.drawingState.tempPoints.push(point);
        
        // 添加临时标记
        const marker = new AMap.Marker({
            position: point,
            icon: new AMap.Icon({
                size: new AMap.Size(8, 8),
                image: 'data:image/svg+xml;base64,' + btoa(`
                    <svg width="8" height="8" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="4" cy="4" r="3" fill="#3b82f6" stroke="white" stroke-width="1"/>
                    </svg>
                `)
            })
        });
        
        this.map.add(marker);
        this.drawingState.tempMarkers.push(marker);
        
        // 如果有两个或更多点，进行路径规划
        if (this.drawingState.tempPoints.length >= 2) {
            this.planRoute();
        }
    }
    
    handlePolylineDoubleClick(e) {
        if (!this.drawingState.isDrawing || this.currentTool !== 'polyline') return;
        
        e.preventDefault();
        this.finishPolylineDraw();
    }
    
    planRoute() {
        if (this.drawingState.tempPoints.length < 2) return;
        
        const points = this.drawingState.tempPoints;
        const startPoint = points[points.length - 2];
        const endPoint = points[points.length - 1];
        
        this.drivingService.search(
            new AMap.LngLat(startPoint[0], startPoint[1]),
            new AMap.LngLat(endPoint[0], endPoint[1]),
            (status, result) => {
                if (status === 'complete' && result.routes && result.routes.length > 0) {
                    const route = result.routes[0];
                    const routePath = [];
                    
                    // 提取路径点
                    route.steps.forEach(step => {
                        step.path.forEach(point => {
                            routePath.push([point.getLng(), point.getLat()]);
                        });
                    });
                    
                    // 更新临时路径
                    if (routePath.length > 0) {
                        const newPath = [...this.drawingState.tempPoints.slice(0, -2), ...routePath];
                        this.updateTempPolyline(newPath);
                    }
                } else {
                    // 路径规划失败，使用直线连接
                    this.updateTempPolyline();
                }
            }
        );
    }
    
    updateTempPolyline(customPath = null) {
        // 移除旧的临时线条
        if (this.drawingState.tempPolyline) {
            this.map.remove(this.drawingState.tempPolyline);
        }
        
        const path = customPath || this.drawingState.tempPoints;
        
        if (path.length >= 2) {
            this.drawingState.tempPolyline = new AMap.Polyline({
                path: path,
                strokeColor: '#3b82f6',
                strokeWeight: 3,
                strokeOpacity: 0.8,
                strokeStyle: 'dashed'
            });
            
            this.map.add(this.drawingState.tempPolyline);
        }
    }
    
    finishPolylineDraw() {
        if (this.drawingState.tempPoints.length < 2) {
            this.clearDrawingState();
            return;
        }
        
        // 创建最终的线条
        let finalPath = this.drawingState.tempPoints;
        
        // 如果启用了路径规划且有临时线条，使用其路径
        if (this.drawingState.tempPolyline && this.snapSettings.enabled) {
            finalPath = this.drawingState.tempPolyline.getPath().map(point => [point.getLng(), point.getLat()]);
        }
        
        const polyline = new AMap.Polyline({
            path: finalPath,
            strokeColor: '#ef4444',
            strokeWeight: 3,
            strokeOpacity: 1,
            cursor: 'pointer'
        });
        
        // 添加到地图和集合
        this.map.add(polyline);
        this.overlays.polylines.push(polyline);
        
        // 清理绘制状态
        this.clearDrawingState();
        
        // 移除事件监听
        this.map.off('click', this.handlePolylineClick);
        this.map.off('dblclick', this.handlePolylineDoubleClick);
        
        console.log('线条绘制完成');
    }

    handleDrawComplete(e) {
        console.log('绘制完成:', e.type);

        const overlay = e.obj;

        // 添加到对应集合
        switch (e.type) {
            case 'marker':
                this.overlays.markers.push(overlay);
                break;
            case 'polyline':
                this.overlays.polylines.push(overlay);
                break;
            case 'polygon':
                this.overlays.polygons.push(overlay);
                break;
            case 'circle':
                this.overlays.circles.push(overlay);
                break;
            case 'rectangle':
                this.overlays.rectangles.push(overlay);
                break;
        }

        // 对于标记点，继续使用当前工具
        // 对于其他图形，切换到选择模式
        if (e.type !== 'marker') {
            setTimeout(() => {
                this.setActiveTool('select');
            }, 100);
        }
    }

    clearDrawingState() {
        // 清除临时标记
        if (this.drawingState.tempMarkers.length > 0) {
            this.map.remove(this.drawingState.tempMarkers);
            this.drawingState.tempMarkers = [];
        }

        // 清除临时线条
        if (this.drawingState.tempPolyline) {
            this.map.remove(this.drawingState.tempPolyline);
            this.drawingState.tempPolyline = null;
        }

        // 重置状态
        this.drawingState.isDrawing = false;
        this.drawingState.tempPoints = [];
    }

    cancelDraw() {
        console.log('取消绘制');

        // 关闭鼠标工具
        this.mouseTool.close();

        // 如果正在进行自定义线条绘制，移除事件监听
        if (this.drawingState.isDrawing && this.currentTool === 'polyline') {
            this.map.off('click', this.handlePolylineClick);
            this.map.off('dblclick', this.handlePolylineDoubleClick);
        }

        // 清除绘制状态
        this.clearDrawingState();

        // 隐藏提示
        this.hideTip();

        // 切换到选择模式
        this.setActiveTool('select');
    }

    clearAll() {
        console.log('清空所有对象');

        // 移除所有覆盖物
        Object.keys(this.overlays).forEach(type => {
            const collection = this.overlays[type];
            collection.forEach(overlay => {
                this.map.remove(overlay);
            });
            collection.length = 0;
        });

        // 清除绘制状态
        this.clearDrawingState();

        console.log('所有对象已清空');
    }

    showTip(message) {
        const tip = document.getElementById('draw-tip');
        tip.textContent = message;
        tip.style.display = 'block';
    }

    hideTip() {
        document.getElementById('draw-tip').style.display = 'none';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.amapDrawTool = new AMapDrawTool();
});
