class MapDrawingApp {
    constructor() {
        this.map = null;
        this.mouseTool = null;
        this.driving = null;
        this.walking = null;
        this.currentTool = 'marker';
        this.snapToRoad = false;
        this.overlays = [];
        this.selectedOverlay = null;
        this.editor = null;
        this.undoStack = [];
        
        this.init();
    }
    
    init() {
        this.initMap();
        this.initTools();
        this.bindEvents();
        this.updateUI();
    }
    
    initMap() {
        // 初始化地图
        this.map = new AMap.Map('map', {
            zoom: 13,
            center: [116.397428, 39.90923],
            viewMode: '2D',
            features: ['bg', 'road', 'building', 'point']
        });
        
        // 初始化鼠标工具
        this.mouseTool = new AMap.MouseTool(this.map);
        
        // 初始化路径规划服务
        this.driving = new AMap.Driving({
            map: this.map,
            hideMarkers: true
        });
        
        this.walking = new AMap.Walking({
            map: this.map,
            hideMarkers: true
        });
        
        // 监听绘制完成
        this.mouseTool.on('draw', (e) => {
            this.onDrawComplete(e);
        });
        
        // 监听地图点击
        this.map.on('click', (e) => {
            this.onMapClick(e);
        });
    }
    
    initTools() {
        // 工具按钮事件
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.setTool(btn.dataset.tool);
            });
        });
        
        // 道路吸附开关
        const snapToggle = document.getElementById('snap-toggle');
        snapToggle.addEventListener('click', () => {
            this.toggleSnapToRoad();
        });
        
        // 操作按钮
        document.getElementById('clear-all').addEventListener('click', () => {
            this.clearAll();
        });
        
        document.getElementById('save-data').addEventListener('click', () => {
            this.saveData();
        });
        
        document.getElementById('load-data').addEventListener('click', () => {
            this.loadData();
        });
    }
    
    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'Escape':
                    this.cancelDraw();
                    break;
                case 'Delete':
                    this.deleteSelected();
                    break;
                case 'z':
                    if (e.ctrlKey || e.metaKey) {
                        this.undo();
                        e.preventDefault();
                    }
                    break;
            }
        });
    }
    
    setTool(toolName) {
        this.currentTool = toolName;
        
        // 更新按钮状态
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tool === toolName);
        });
        
        // 关闭当前工具
        this.mouseTool.close();
        this.clearSelection();
        
        // 启动新工具
        switch(toolName) {
            case 'marker':
                this.mouseTool.marker({
                    icon: new AMap.Icon({
                        size: new AMap.Size(25, 34),
                        image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                    })
                });
                this.showTip('点击地图添加标记点');
                break;
            case 'polyline':
                this.mouseTool.polyline({
                    strokeColor: '#3366FF',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                });
                this.showTip('点击地图开始绘制线条，双击结束');
                break;
            case 'polygon':
                this.mouseTool.polygon({
                    fillColor: '#00B2D5',
                    fillOpacity: 0.3,
                    strokeColor: '#006600',
                    strokeWeight: 2
                });
                this.showTip('点击地图开始绘制多边形，双击结束');
                break;
            case 'rectangle':
                this.mouseTool.rectangle({
                    fillColor: '#00B2D5',
                    fillOpacity: 0.3,
                    strokeColor: '#006600',
                    strokeWeight: 2
                });
                this.showTip('点击并拖动绘制矩形');
                break;
            case 'circle':
                this.mouseTool.circle({
                    fillColor: '#00B2D5',
                    fillOpacity: 0.3,
                    strokeColor: '#006600',
                    strokeWeight: 2
                });
                this.showTip('点击并拖动绘制圆形');
                break;
            case 'select':
                this.hideTip();
                break;
        }
        
        this.updateUI();
    }
    
    toggleSnapToRoad() {
        this.snapToRoad = !this.snapToRoad;
        const toggle = document.getElementById('snap-toggle');
        toggle.classList.toggle('active', this.snapToRoad);
        this.updateUI();
    }
    
    async onDrawComplete(e) {
        const overlay = e.obj;
        
        // 如果是线条且开启了道路吸附
        if (this.snapToRoad && (e.type === 'polyline')) {
            await this.snapPolylineToRoad(overlay);
        }
        
        // 添加到覆盖物数组
        this.overlays.push({
            id: this.generateId(),
            type: e.type,
            overlay: overlay,
            data: this.getOverlayData(overlay, e.type)
        });
        
        // 添加点击事件
        overlay.on('click', () => {
            this.selectOverlay(overlay);
        });
        
        // 保存到撤销栈
        this.saveToUndoStack();
        
        this.updateUI();
        this.updateObjectList();
        
        // 对于标记点，继续使用当前工具
        if (e.type !== 'marker') {
            setTimeout(() => {
                this.setTool('select');
            }, 100);
        }
    }
    
    async snapPolylineToRoad(polyline) {
        const path = polyline.getPath();
        if (path.length < 2) return;
        
        try {
            // 使用步行路径规划来获取道路吸附
            const result = await new Promise((resolve, reject) => {
                this.walking.search(path[0], path[path.length - 1], {
                    waypoints: path.slice(1, -1)
                }, (status, result) => {
                    if (status === 'complete') {
                        resolve(result);
                    } else {
                        reject(status);
                    }
                });
            });
            
            if (result.routes && result.routes[0]) {
                const route = result.routes[0];
                const newPath = [];
                
                route.steps.forEach(step => {
                    step.path.forEach(point => {
                        newPath.push([point.lng, point.lat]);
                    });
                });
                
                if (newPath.length > 0) {
                    polyline.setPath(newPath);
                }
            }
        } catch (error) {
            console.warn('道路吸附失败:', error);
        }
    }
    
    selectOverlay(overlay) {
        this.clearSelection();
        this.selectedOverlay = overlay;
        
        // 高亮选中的覆盖物
        if (overlay.setOptions) {
            const originalOptions = overlay.getOptions();
            overlay.originalOptions = originalOptions;
            
            if (overlay.CLASS_NAME === 'AMap.Marker') {
                // 标记点高亮
                overlay.setAnimation('AMAP_ANIMATION_BOUNCE');
            } else {
                // 其他图形高亮
                overlay.setOptions({
                    strokeColor: '#FF0000',
                    strokeWeight: (originalOptions.strokeWeight || 2) + 2
                });
            }
        }
        
        this.updateUI();
    }
    
    clearSelection() {
        if (this.selectedOverlay) {
            // 恢复原始样式
            if (this.selectedOverlay.originalOptions) {
                this.selectedOverlay.setOptions(this.selectedOverlay.originalOptions);
            }
            if (this.selectedOverlay.setAnimation) {
                this.selectedOverlay.setAnimation('AMAP_ANIMATION_NONE');
            }
            this.selectedOverlay = null;
        }
        
        if (this.editor) {
            this.editor.close();
            this.editor = null;
        }
    }
    
    editSelected() {
        if (!this.selectedOverlay) return;
        
        // 创建编辑器
        this.editor = new AMap.PolyEditor(this.map, this.selectedOverlay);
        this.editor.open();
        
        this.editor.on('end', () => {
            this.saveToUndoStack();
            this.updateObjectList();
        });
    }
    
    deleteSelected() {
        if (!this.selectedOverlay) return;
        
        // 从地图移除
        this.map.remove(this.selectedOverlay);
        
        // 从数组移除
        this.overlays = this.overlays.filter(item => item.overlay !== this.selectedOverlay);
        
        this.clearSelection();
        this.saveToUndoStack();
        this.updateUI();
        this.updateObjectList();
    }
    
    clearAll() {
        if (confirm('确定要清空所有绘制对象吗？')) {
            this.overlays.forEach(item => {
                this.map.remove(item.overlay);
            });
            this.overlays = [];
            this.clearSelection();
            this.saveToUndoStack();
            this.updateUI();
            this.updateObjectList();
        }
    }
    
    saveData() {
        const data = this.overlays.map(item => ({
            id: item.id,
            type: item.type,
            data: item.data
        }));
        
        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `map-data-${new Date().toISOString().slice(0, 10)}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.setStatus('数据已保存');
    }
    
    loadData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        this.loadFromData(data);
                        this.setStatus('数据已加载');
                    } catch (error) {
                        alert('文件格式错误');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }
    
    loadFromData(data) {
        this.clearAll();
        
        data.forEach(item => {
            let overlay;
            
            switch(item.type) {
                case 'marker':
                    overlay = new AMap.Marker({
                        position: item.data.position,
                        icon: new AMap.Icon({
                            size: new AMap.Size(25, 34),
                            image: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
                        })
                    });
                    break;
                case 'polyline':
                    overlay = new AMap.Polyline({
                        path: item.data.path,
                        strokeColor: '#3366FF',
                        strokeWeight: 4,
                        strokeOpacity: 0.8
                    });
                    break;
                case 'polygon':
                    overlay = new AMap.Polygon({
                        path: item.data.path,
                        fillColor: '#00B2D5',
                        fillOpacity: 0.3,
                        strokeColor: '#006600',
                        strokeWeight: 2
                    });
                    break;
                case 'rectangle':
                    overlay = new AMap.Rectangle({
                        bounds: new AMap.Bounds(item.data.southWest, item.data.northEast),
                        fillColor: '#00B2D5',
                        fillOpacity: 0.3,
                        strokeColor: '#006600',
                        strokeWeight: 2
                    });
                    break;
                case 'circle':
                    overlay = new AMap.Circle({
                        center: item.data.center,
                        radius: item.data.radius,
                        fillColor: '#00B2D5',
                        fillOpacity: 0.3,
                        strokeColor: '#006600',
                        strokeWeight: 2
                    });
                    break;
            }
            
            if (overlay) {
                this.map.add(overlay);
                this.overlays.push({
                    id: item.id,
                    type: item.type,
                    overlay: overlay,
                    data: item.data
                });
                
                overlay.on('click', () => {
                    this.selectOverlay(overlay);
                });
            }
        });
        
        this.updateUI();
        this.updateObjectList();
    }
    
    getOverlayData(overlay, type) {
        switch(type) {
            case 'marker':
                return { position: overlay.getPosition() };
            case 'polyline':
                return { path: overlay.getPath() };
            case 'polygon':
                return { path: overlay.getPath() };
            case 'rectangle':
                const bounds = overlay.getBounds();
                return {
                    southWest: bounds.getSouthWest(),
                    northEast: bounds.getNorthEast()
                };
            case 'circle':
                return {
                    center: overlay.getCenter(),
                    radius: overlay.getRadius()
                };
            default:
                return {};
        }
    }
    
    generateId() {
        return 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    saveToUndoStack() {
        // 简化的撤销功能
        this.undoStack.push({
            overlays: this.overlays.map(item => ({...item})),
            timestamp: Date.now()
        });
        
        // 限制撤销栈大小
        if (this.undoStack.length > 20) {
            this.undoStack.shift();
        }
    }
    
    undo() {
        if (this.undoStack.length > 1) {
            this.undoStack.pop(); // 移除当前状态
            const prevState = this.undoStack[this.undoStack.length - 1];
            
            // 清除当前覆盖物
            this.overlays.forEach(item => {
                this.map.remove(item.overlay);
            });
            
            // 恢复之前状态
            this.loadFromData(prevState.overlays);
            this.setStatus('已撤销');
        }
    }
    
    onMapClick(e) {
        if (this.currentTool === 'select') {
            this.clearSelection();
        }
    }
    
    cancelDraw() {
        this.mouseTool.close();
        this.hideTip();
        this.setTool('select');
        this.setStatus('已取消绘制');
    }
    
    showTip(message) {
        const tip = document.getElementById('tip');
        tip.textContent = message;
        tip.style.display = 'block';
        setTimeout(() => this.hideTip(), 3000);
    }
    
    hideTip() {
        document.getElementById('tip').style.display = 'none';
    }
    
    setStatus(message) {
        document.getElementById('status').textContent = message;
        setTimeout(() => {
            document.getElementById('status').textContent = '就绪';
        }, 2000);
    }
    
    updateUI() {
        // 更新工具名称
        const toolNames = {
            marker: '标记点',
            polyline: '绘制线',
            polygon: '绘制面',
            rectangle: '矩形',
            circle: '圆形',
            select: '选择'
        };
        document.getElementById('current-tool').textContent = toolNames[this.currentTool];
        
        // 更新道路吸附状态
        document.getElementById('snap-status').textContent = this.snapToRoad ? '开启' : '关闭';
        
        // 更新对象数量
        document.getElementById('object-count').textContent = this.overlays.length;
    }
    
    updateObjectList() {
        const container = document.getElementById('objects-container');
        
        if (this.overlays.length === 0) {
            container.innerHTML = '<div style="text-align: center; color: #999; font-size: 12px; padding: 20px;">暂无绘制对象</div>';
            return;
        }
        
        const typeNames = {
            marker: '📍 标记点',
            polyline: '📏 线条',
            polygon: '🔷 多边形',
            rectangle: '⬜ 矩形',
            circle: '⭕ 圆形'
        };
        
        container.innerHTML = this.overlays.map((item, index) => `
            <div class="object-item">
                <span class="type">${typeNames[item.type]} ${index + 1}</span>
                <div class="object-actions">
                    <button class="edit-btn" onclick="app.editOverlay('${item.id}')">编辑</button>
                    <button class="delete-btn" onclick="app.deleteOverlay('${item.id}')">删除</button>
                </div>
            </div>
        `).join('');
    }
    
    editOverlay(id) {
        const item = this.overlays.find(item => item.id === id);
        if (item) {
            this.selectOverlay(item.overlay);
            this.editSelected();
        }
    }
    
    deleteOverlay(id) {
        const item = this.overlays.find(item => item.id === id);
        if (item) {
            this.selectOverlay(item.overlay);
            this.deleteSelected();
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    window.app = new MapDrawingApp();
});
