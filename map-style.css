* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
    overflow: hidden;
}

.header {
    background-color: #2c3e50;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 1.5rem;
    margin: 0;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #7f8c8d;
    color: white;
}

.btn-secondary:hover {
    background-color: #6c7a7d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.sidebar {
    width: 280px;
    background-color: #ecf0f1;
    padding: 15px;
    overflow-y: auto;
    border-right: 1px solid #ddd;
}

.map-container {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
}

#map {
    width: 100%;
    height: 100%;
    z-index: 1;
}

.map-controls {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    z-index: 1000;
    display: flex;
    gap: 15px;
}

.tool-section, .info-section, .style-section {
    margin-bottom: 20px;
    background-color: white;
    border-radius: 6px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tool-section h3, .info-section h3, .style-section h3 {
    margin-bottom: 10px;
    font-size: 1.1rem;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.tool-btn {
    padding: 8px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.tool-btn:hover {
    background-color: #f1f1f1;
    border-color: #bbb;
}

.tool-btn.active {
    background-color: #3498db;
    color: white;
    border-color: #2980b9;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px dashed #eee;
}

.info-label {
    font-weight: bold;
}

.style-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.style-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.style-group label {
    min-width: 100px;
    font-size: 0.9rem;
}

input[type="color"] {
    width: 40px;
    height: 25px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

input[type="range"] {
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid #ddd;
    }
    
    .tool-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
}
