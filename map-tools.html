<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图绘制工具集合</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 40px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        
        .app-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .app-card {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 25px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .app-card:hover {
            border-color: #667eea;
            background: #f0f2ff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .app-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.3em;
        }
        
        .app-card p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.5;
        }
        
        .features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .features li {
            padding: 3px 0;
            color: #555;
            font-size: 0.9em;
        }
        
        .features li::before {
            content: '✓ ';
            color: #28a745;
            font-weight: bold;
        }
        
        .recommended {
            position: relative;
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }
        
        .recommended::before {
            content: '推荐';
            position: absolute;
            top: -10px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 5px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            color: #999;
            font-size: 0.9em;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .quick-start {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .quick-start h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .quick-start p {
            margin: 0;
            color: #424242;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 地图绘制工具</h1>
        <p class="subtitle">基于高德地图的专业绘图工具集合</p>
        
        <div class="quick-start">
            <h4>🚀 快速开始</h4>
            <p>推荐使用 <strong>专业绘制工具</strong>，它包含完整的道路吸附功能和现代化的用户界面，支持点线面绘制、图形编辑、数据保存等专业功能。</p>
        </div>
        
        <div class="app-grid">
            <a href="amap-drawing-app.html" class="app-card recommended">
                <h3>🚀 专业绘制工具</h3>
                <p>功能最完整的地图绘制应用，支持道路吸附和专业编辑功能</p>
                <ul class="features">
                    <li>道路自动吸附功能</li>
                    <li>点线面完整绘制</li>
                    <li>图形编辑和删除</li>
                    <li>数据保存和加载</li>
                    <li>撤销重做功能</li>
                    <li>现代化界面设计</li>
                    <li>对象列表管理</li>
                    <li>快捷键支持</li>
                </ul>
            </a>
            
            <a href="amap-draw-fixed.html" class="app-card">
                <h3>🔧 基础绘制工具</h3>
                <p>简洁的地图绘制工具，适合快速绘图需求</p>
                <ul class="features">
                    <li>基础绘制功能</li>
                    <li>标记点、线条、多边形</li>
                    <li>矩形和圆形工具</li>
                    <li>简单清爽界面</li>
                    <li>快速上手</li>
                </ul>
            </a>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">📋 功能说明</h4>
            <div style="color: #856404; font-size: 0.9em; line-height: 1.5;">
                <strong>道路吸附功能：</strong> 开启后，绘制的线条会自动贴合最近的道路，适合绘制路线规划<br>
                <strong>编辑功能：</strong> 支持对已绘制的图形进行编辑、移动、删除等操作<br>
                <strong>数据管理：</strong> 可以保存绘制的数据为JSON文件，也可以加载之前保存的数据
            </div>
        </div>
        
        <div class="footer">
            <p>💡 提示：专业绘制工具包含完整的道路吸附和编辑功能，推荐优先使用</p>
            <p>🗺️ 基于高德地图 API 开发 | 支持现代浏览器 | 响应式设计</p>
        </div>
    </div>
</body>
</html>
